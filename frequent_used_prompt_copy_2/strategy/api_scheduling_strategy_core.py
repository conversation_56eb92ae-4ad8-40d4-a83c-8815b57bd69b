# -*- coding: utf-8 -*-
"""
API调度策略核心架构 - 精简版
核心组件：性能监控、自适应并发控制、令牌桶限流、优先级重试队列
"""
import os
import json
import requests
import time
import threading
import random
import heapq
import numpy as np
from collections import defaultdict

# --- 核心配置 ---
API_URL = "https://api.siliconflow.cn/v1/chat/completions"
API_TOKEN = os.environ.get("SILICONFLOW_API_TOKEN", "YOUR_TOKEN_HERE")
DEFAULT_MODEL = "Pro/deepseek-ai/DeepSeek-V3"
MAX_RETRIES = 10
API_RATE_LIMIT = 30

# --- 性能指标收集器 ---
class PerformanceMetrics:
    """性能指标收集和监控"""
    def __init__(self, window_size=100):
        self.window_size = window_size
        self.lock = threading.Lock()
        self.response_times = []
        self.success_count = 0
        self.total_count = 0
        self.error_counts = defaultdict(int)

    def record_request_complete(self, start_time, success=True, error_type=None):
        """记录请求完成"""
        with self.lock:
            response_time = time.time() - start_time
            self.response_times.append(response_time)
            
            if len(self.response_times) > self.window_size:
                self.response_times.pop(0)
            
            self.total_count += 1
            if success:
                self.success_count += 1
            elif error_type:
                self.error_counts[error_type] += 1

    def get_performance_summary(self):
        """获取性能摘要"""
        with self.lock:
            avg_response_time = np.mean(self.response_times) if self.response_times else 0
            success_rate = (self.success_count / self.total_count) if self.total_count > 0 else 1.0
            return {
                'avg_response_time': avg_response_time,
                'success_rate': success_rate,
                'total_requests': len(self.response_times),
                'error_counts': dict(self.error_counts)
            }

# --- 自适应并发控制器 ---
class AdaptiveConcurrencyController:
    """基于性能指标的自适应并发控制"""
    def __init__(self, initial_concurrency=3, min_concurrency=1, max_concurrency=10):
        self.current_concurrency = initial_concurrency
        self.min_concurrency = min_concurrency
        self.max_concurrency = max_concurrency
        self.lock = threading.Lock()
        self.metrics = PerformanceMetrics()
        self.last_adjustment_time = time.time()
        self.adjustment_cooldown = 30

    def adjust_concurrency(self):
        """根据性能指标调整并发数"""
        current_time = time.time()
        if (current_time - self.last_adjustment_time) < self.adjustment_cooldown:
            return self.current_concurrency

        performance = self.metrics.get_performance_summary()
        if performance['total_requests'] < 5:
            return self.current_concurrency

        with self.lock:
            old_concurrency = self.current_concurrency
            success_rate = performance['success_rate']
            avg_response_time = performance['avg_response_time']

            # 简化的调整逻辑
            if success_rate < 0.8:
                self.current_concurrency = max(self.min_concurrency, self.current_concurrency - 1)
            elif success_rate > 0.95 and avg_response_time < 2.0:
                self.current_concurrency = min(self.max_concurrency, self.current_concurrency + 1)

            if self.current_concurrency != old_concurrency:
                self.last_adjustment_time = current_time
                print(f"🎛️ 并发数调整: {old_concurrency} -> {self.current_concurrency}")

            return self.current_concurrency

    def get_current_concurrency(self):
        with self.lock:
            return self.current_concurrency

    def record_request(self, start_time, success=True, error_type=None):
        self.metrics.record_request_complete(start_time, success, error_type)

# --- 令牌桶限流器 ---
class TokenBucketRateLimiter:
    """令牌桶算法实现的速率限制器"""
    def __init__(self, tokens_per_minute=60):
        self.tokens_per_minute = tokens_per_minute
        self.tokens_per_second = tokens_per_minute / 60.0
        self.bucket_capacity = int(tokens_per_minute * 1.5)
        self.lock = threading.Lock()
        self.current_tokens = self.bucket_capacity
        self.last_refill_time = time.time()
        self.rate_limit_hit_count = 0

    def _refill_tokens(self):
        """重新填充令牌"""
        now = time.time()
        time_passed = now - self.last_refill_time
        new_tokens = time_passed * self.tokens_per_second
        
        if new_tokens > 0:
            self.current_tokens = min(self.bucket_capacity, self.current_tokens + new_tokens)
            self.last_refill_time = now

    def get_token(self):
        """获取一个令牌"""
        with self.lock:
            self._refill_tokens()
            
            if self.current_tokens < 1:
                required_time = (1 - self.current_tokens) / self.tokens_per_second
                self.lock.release()
                try:
                    time.sleep(required_time + random.uniform(0, 0.1))
                finally:
                    self.lock.acquire()
                self._refill_tokens()
            
            if self.current_tokens >= 1:
                self.current_tokens -= 1
                return True
            return False

    def wait_if_needed(self):
        """等待直到获取到令牌"""
        while not self.get_token():
            time.sleep(0.1)
        return time.time()  # 返回请求开始时间

    def report_rate_limit_hit(self):
        """报告遇到了速率限制"""
        with self.lock:
            self.rate_limit_hit_count += 1

# --- 优先级重试队列 ---
class PriorityRetryQueue:
    """基于重试次数的优先级队列"""
    def __init__(self):
        self.queue = []
        self.entry_count = 0
        self.lock = threading.Lock()
        self.retry_counts = {}

    def add_task(self, task_id, task_data, retry_count=0):
        """添加任务到优先级队列"""
        with self.lock:
            self.retry_counts[task_id] = retry_count
            priority = -retry_count  # 重试次数多的优先级高
            heapq.heappush(self.queue, (priority, self.entry_count, task_id, task_data))
            self.entry_count += 1

    def get_all_tasks_sorted(self):
        """获取所有任务，按优先级排序"""
        with self.lock:
            sorted_tasks = sorted(self.queue)
            tasks = [(item[2], item[3], -item[0]) for item in sorted_tasks]  # (task_id, task_data, retry_count)
            self.queue = []
            self.retry_counts = {}
            return tasks

    def is_empty(self):
        with self.lock:
            return len(self.queue) == 0

# --- API调用核心函数 ---
def call_api_with_retry(payload, max_retries=MAX_RETRIES):
    """带重试机制的API调用"""
    headers = {
        "Authorization": f"Bearer {API_TOKEN}",
        "Content-Type": "application/json"
    }
    
    for attempt in range(max_retries):
        try:
            response = requests.post(API_URL, headers=headers, json=payload, timeout=240)
            response.raise_for_status()
            return response.json(), None
            
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 429:
                wait_time = min(60, 2 ** attempt)
                print(f"Rate limit hit, waiting {wait_time}s...")
                time.sleep(wait_time)
                continue
            return None, f"HTTP error {e.response.status_code}"
            
        except requests.exceptions.RequestException as e:
            if attempt < max_retries - 1:
                wait_time = min(60, 2 ** attempt)
                time.sleep(wait_time)
                continue
            return None, f"Request failed: {str(e)}"
    
    return None, "Max retries exceeded"

# --- 使用示例 ---
def example_usage():
    """使用示例"""
    # 初始化组件
    rate_limiter = TokenBucketRateLimiter(API_RATE_LIMIT)
    concurrency_controller = AdaptiveConcurrencyController()
    retry_queue = PriorityRetryQueue()
    
    # API调用示例
    payload = {
        "model": DEFAULT_MODEL,
        "messages": [{"role": "user", "content": "Hello"}],
        "temperature": 0.5,
        "max_tokens": 1000
    }
    
    # 等待令牌并调用API
    start_time = rate_limiter.wait_if_needed()
    result, error = call_api_with_retry(payload)
    
    # 记录结果
    success = result is not None
    concurrency_controller.record_request(start_time, success, "api_error" if error else None)
    
    if not success:
        retry_queue.add_task("task_1", payload, retry_count=1)
    
    # 调整并发数
    new_concurrency = concurrency_controller.adjust_concurrency()
    print(f"Current concurrency: {new_concurrency}")

if __name__ == "__main__":
    example_usage()
