# -*- coding: utf-8 -*-
"""
视频描述任务核心架构 - 精简版
包含：API密钥管理、多线程处理、重试机制、进度管理
"""
import os
import json
import time
import threading
import random
from typing import List, Dict, Optional, Any
from google import genai
from google.genai import types
from google.genai.errors import ClientError

# --- 核心配置 ---
GOOGLE_API_KEYS = [
    'YOUR_API_KEY_1',
    'YOUR_API_KEY_2',
    # 添加更多API密钥
]

MAX_FILE_SIZE_MB = 19
MAX_RETRIES = 3
BASE_DELAY = 5
API_COOLDOWN_TIME = 180
MAX_CONSECUTIVE_FAILURES = 2

VIDEO_ANALYSIS_PROMPT = """Analyze this video and return a JSON response:
{
    "description": "Describe what you see in the video"
}
Answer in valid JSON format only."""

# --- API密钥管理器 ---
class APIKeyManager:
    """API密钥管理器 - 支持多线程和智能轮询"""
    
    def __init__(self, api_keys: List[str] = None):
        self.api_keys = api_keys or GOOGLE_API_KEYS
        self.lock = threading.RLock()
        self.thread_clients = {}  # {thread_id: client}
        self.thread_key_assignments = {}  # {thread_id: key_index}
        self.key_stats = {}
        self.failed_keys = set()
        self.last_failure_time = {}
        self.consecutive_failures = {}
        
        # 初始化密钥统计
        for key in self.api_keys:
            self.key_stats[key] = {
                'total_requests': 0,
                'successful_requests': 0,
                'failed_requests': 0,
                'consecutive_failures': 0
            }
            self.consecutive_failures[key] = 0
        
        print(f"🔑 初始化API密钥管理器，共 {len(self.api_keys)} 个密钥")
    
    def get_client_for_thread(self) -> Optional[genai.Client]:
        """为当前线程获取API客户端"""
        thread_id = threading.current_thread().ident
        
        with self.lock:
            # 检查当前线程是否已有客户端
            if thread_id in self.thread_clients:
                return self.thread_clients[thread_id]
            
            # 为新线程分配密钥
            available_key_index = self._find_available_key()
            if available_key_index is not None:
                try:
                    api_key = self.api_keys[available_key_index]
                    client = genai.Client(api_key=api_key)
                    self.thread_clients[thread_id] = client
                    self.thread_key_assignments[thread_id] = available_key_index
                    print(f"🔄 线程 {thread_id} 使用API密钥: {api_key[:20]}...")
                    return client
                except Exception as e:
                    print(f"❌ 创建API客户端失败: {e}")
                    self._mark_key_failed(self.api_keys[available_key_index])
                    return None
            
            return None
    
    def _find_available_key(self) -> Optional[int]:
        """查找可用的API密钥"""
        current_time = time.time()
        
        # 检查冷却时间，恢复失败的密钥
        keys_to_restore = []
        for key in list(self.failed_keys):
            if (key in self.last_failure_time and 
                current_time - self.last_failure_time[key] > API_COOLDOWN_TIME):
                keys_to_restore.append(key)
        
        for key in keys_to_restore:
            self.failed_keys.discard(key)
            self.consecutive_failures[key] = 0
            print(f"🔄 API密钥恢复可用: {key[:20]}...")
        
        # 查找可用密钥
        available_indices = []
        for i, key in enumerate(self.api_keys):
            if key not in self.failed_keys:
                available_indices.append(i)
        
        if not available_indices:
            return None
        
        # 简单轮询选择
        return available_indices[0]
    
    def _mark_key_failed(self, api_key: str):
        """标记密钥失败"""
        self.consecutive_failures[api_key] += 1
        self.last_failure_time[api_key] = time.time()
        
        if self.consecutive_failures[api_key] >= MAX_CONSECUTIVE_FAILURES:
            self.failed_keys.add(api_key)
            print(f"❌ API密钥暂时禁用: {api_key[:20]}...")
    
    def record_success(self, thread_id: int):
        """记录成功请求"""
        with self.lock:
            if thread_id in self.thread_key_assignments:
                key_index = self.thread_key_assignments[thread_id]
                api_key = self.api_keys[key_index]
                self.key_stats[api_key]['successful_requests'] += 1
                self.consecutive_failures[api_key] = 0
    
    def record_failure(self, thread_id: int):
        """记录失败请求"""
        with self.lock:
            if thread_id in self.thread_key_assignments:
                key_index = self.thread_key_assignments[thread_id]
                api_key = self.api_keys[key_index]
                self.key_stats[api_key]['failed_requests'] += 1
                self._mark_key_failed(api_key)

# --- 视频处理器 ---
class VideoProcessor:
    """视频处理器 - 支持重试和错误处理"""
    
    def __init__(self, api_manager: APIKeyManager):
        self.api_manager = api_manager
    
    def validate_video_file(self, video_path: str) -> bool:
        """验证视频文件"""
        if not os.path.exists(video_path):
            print(f"❌ 视频文件不存在: {video_path}")
            return False
        
        file_size_mb = os.path.getsize(video_path) / (1024 * 1024)
        if file_size_mb > MAX_FILE_SIZE_MB:
            print(f"❌ 视频文件过大: {file_size_mb:.2f}MB > {MAX_FILE_SIZE_MB}MB")
            return False
        
        return True
    
    def process_video_with_retry(self, video_path: str, prompt: str = VIDEO_ANALYSIS_PROMPT) -> Optional[str]:
        """带重试机制的视频处理"""
        if not self.validate_video_file(video_path):
            return None
        
        thread_id = threading.current_thread().ident
        
        for attempt in range(MAX_RETRIES):
            client = self.api_manager.get_client_for_thread()
            if not client:
                print(f"❌ 无法获取API客户端")
                return None
            
            try:
                print(f"🎬 处理视频 (尝试 {attempt + 1}/{MAX_RETRIES}): {os.path.basename(video_path)}")
                
                # 读取视频文件
                with open(video_path, 'rb') as f:
                    video_bytes = f.read()
                
                # 调用API
                response = client.models.generate_content(
                    model="models/gemini-2.5-pro",
                    contents=types.Content(
                        parts=[
                            types.Part(
                                inline_data=types.Blob(data=video_bytes, mime_type='video/mp4')
                            ),
                            types.Part(text=prompt)
                        ]
                    )
                )
                
                self.api_manager.record_success(thread_id)
                return response.text
                
            except ClientError as e:
                self.api_manager.record_failure(thread_id)
                
                if e.status_code == 429:  # 配额限制
                    retry_delay = BASE_DELAY * (2 ** attempt)
                    print(f"⚠️ 配额限制，等待 {retry_delay} 秒...")
                    
                    if attempt < MAX_RETRIES - 1:
                        time.sleep(retry_delay)
                        continue
                else:
                    print(f"❌ API错误 (状态码: {e.status_code}): {str(e)}")
                    break
                    
            except Exception as e:
                self.api_manager.record_failure(thread_id)
                print(f"❌ 处理错误: {str(e)}")
                
                if attempt < MAX_RETRIES - 1:
                    time.sleep(BASE_DELAY)
                    continue
                break
        
        return None

# --- 进度管理器 ---
class ProgressManager:
    """进度管理器 - 支持断点续传"""
    
    def __init__(self, progress_file: str = "progress.json"):
        self.progress_file = progress_file
        self.lock = threading.Lock()
        self.processed_files = set()
        self.failed_files = set()
        self.load_progress()
    
    def load_progress(self):
        """加载进度"""
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.processed_files = set(data.get('processed_files', []))
                    self.failed_files = set(data.get('failed_files', []))
                print(f"📊 加载进度: 已处理 {len(self.processed_files)} 个文件")
            except Exception as e:
                print(f"⚠️ 加载进度失败: {e}")
    
    def save_progress(self):
        """保存进度"""
        with self.lock:
            try:
                data = {
                    'processed_files': list(self.processed_files),
                    'failed_files': list(self.failed_files),
                    'timestamp': time.time()
                }
                with open(self.progress_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
            except Exception as e:
                print(f"⚠️ 保存进度失败: {e}")
    
    def is_processed(self, file_path: str) -> bool:
        """检查文件是否已处理"""
        return file_path in self.processed_files
    
    def mark_processed(self, file_path: str):
        """标记文件已处理"""
        with self.lock:
            self.processed_files.add(file_path)
    
    def mark_failed(self, file_path: str):
        """标记文件处理失败"""
        with self.lock:
            self.failed_files.add(file_path)

# --- 使用示例 ---
def example_usage():
    """使用示例"""
    # 初始化组件
    api_manager = APIKeyManager(GOOGLE_API_KEYS)
    video_processor = VideoProcessor(api_manager)
    progress_manager = ProgressManager()
    
    # 处理单个视频
    video_path = "/path/to/your/video.mp4"
    
    if not progress_manager.is_processed(video_path):
        result = video_processor.process_video_with_retry(video_path)
        
        if result:
            print(f"✅ 处理成功: {os.path.basename(video_path)}")
            progress_manager.mark_processed(video_path)
            
            # 保存结果
            output_path = video_path.replace('.mp4', '_analysis.json')
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump({'result': result}, f, ensure_ascii=False, indent=2)
        else:
            print(f"❌ 处理失败: {os.path.basename(video_path)}")
            progress_manager.mark_failed(video_path)
        
        progress_manager.save_progress()
    else:
        print(f"⏭️ 跳过已处理文件: {os.path.basename(video_path)}")

if __name__ == "__main__":
    example_usage()
