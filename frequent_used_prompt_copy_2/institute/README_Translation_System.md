# 电商翻译系统 (E-commerce Translation System)

基于 OpenRouter API 的专业电商产品描述翻译系统，专门针对鞋类产品优化，支持多语言高质量翻译。

## 🚀 功能特性

### 核心功能
- **多语言支持**: 英语、德语、法语、西班牙语、意大利语
- **智能模型分配**: 根据目标语言自动选择最优模型
- **电商优化**: 专门针对Amazon等电商平台的产品描述优化
- **质量保证**: 内置翻译质量检查和评估系统
- **批量处理**: 支持一次性翻译到多种语言

### 语言特定优化
- **德语**: 使用商业术语，避免医疗用词，正确的复合词大写
- **法语**: 性别一致性，自然缩写，商业正式程度
- **西班牙语/意大利语**: 中性标准语言，商业友好表达

## 📁 文件结构

```
institute/
├── openrouter_api_img_core.py          # 主要API核心文件（已扩展翻译功能）
├── ecommerce_translation_config.py     # 翻译系统配置文件
├── translation_demo.py                 # 演示脚本
└── README_Translation_System.md        # 本说明文件
```

## 🛠️ 安装和配置

### 1. 依赖安装
```bash
pip install requests
```

### 2. API密钥配置
在 `openrouter_api_img_core.py` 中设置您的 OpenRouter API 密钥：
```python
API_KEY = "your_openrouter_api_key_here"
```

或者设置环境变量：
```bash
export OPENROUTER_API_KEY="your_api_key_here"
```

### 3. 模型配置
系统默认使用以下模型分配：
- **德语、西班牙语、意大利语**: `openai/chatgpt-4o-latest`
- **法语**: `google/gemini-2.5-pro`

## 💻 使用方法

### 基本使用

```python
from openrouter_api_img_core import EcommerceTranslationSystem

# 初始化翻译系统
translator = EcommerceTranslationSystem(api_key="your_api_key")

# 单语言翻译
result = translator.translate_text(
    text="Premium running shoes with advanced cushioning",
    target_language="German",
    origin_language="English"
)

if result:
    translation = translator.extract_translation_content(result)
    print(translation)
```

### 批量翻译

```python
# 批量翻译到多种语言
batch_results = translator.batch_translate(
    text="Your product description here",
    target_languages=["German", "French", "Spanish", "Italian"],
    origin_language="English"
)

for language, result in batch_results.items():
    if result:
        translation = translator.extract_translation_content(result)
        print(f"{language}: {translation}")
```

### 运行演示

```bash
# 运行完整演示
python translation_demo.py

# 或运行主文件选择模式
python openrouter_api_img_core.py
```

## 🎯 翻译提示模板

系统使用专门优化的提示模板，包含以下要求：

### 语言质量标准
- 提供自然、母语水平的翻译
- 使用适合鞋类/时尚行业的术语
- 遵循目标语言的电商惯例和消费者期望
- 确保语法准确性和正确拼写

### 内容保持
- 保持所有原始含义、技术规格和卖点
- 保留产品特性、优势和关键属性
- 保持测量单位、尺寸和技术术语的准确性

### 电商优化
- 使用促进购买决策的消费者友好语言
- 采用目标语言在线购物者熟悉的术语
- 确保翻译支持产品搜索性和转化率

## 🔍 质量检查系统

### 德语质量检查
- 检测不当医疗术语使用
- 验证复合词大写规则
- 确保商业语域适当性

### 法语质量检查
- 检查性别一致性
- 验证自然缩写使用
- 确保商业正式程度

### 完整性检查
- 比较原文和译文长度
- 检测可能的内容遗漏或冗余
- 评估翻译完整性得分

## 📊 示例产品描述

系统包含三种预设产品描述示例：

1. **跑步鞋** (`running_shoes`)
2. **休闲运动鞋** (`casual_sneakers`)  
3. **登山靴** (`hiking_boots`)

## 🔧 高级配置

### 自定义模型分配
```python
# 修改语言模型映射
translator.LANGUAGE_MODEL_MAPPING['german'] = "your_preferred_model"
```

### 翻译参数调整
```python
result = translator.translate_text(
    text="Your text",
    target_language="German",
    temperature=0.2,  # 更低温度获得更一致的翻译
    max_tokens=4000
)
```

### 质量评估
```python
from ecommerce_translation_config import TranslationQualityChecker

checker = TranslationQualityChecker()
quality_report = checker.check_german_quality(translation)
print(f"质量评分: {quality_report['score']}/100")
```

## 📈 性能优化建议

1. **批量处理**: 对于多个产品，使用批量翻译功能
2. **缓存结果**: 对于重复内容，考虑缓存翻译结果
3. **并发控制**: 注意API调用频率限制
4. **错误处理**: 实现适当的重试机制

## 🐛 故障排除

### 常见问题

1. **API密钥错误**
   - 检查密钥是否正确设置
   - 确认密钥有效且有足够余额

2. **翻译质量问题**
   - 检查原文是否清晰完整
   - 考虑调整temperature参数
   - 使用质量检查工具评估

3. **模型不可用**
   - 检查模型名称是否正确
   - 确认模型在OpenRouter上可用

## 📝 更新日志

### v1.0.0 (2025-08-01)
- 初始版本发布
- 支持5种语言翻译
- 集成质量检查系统
- 添加批量翻译功能
- 包含完整演示和配置

## 📄 许可证

本项目仅供学习和研究使用。请确保遵守 OpenRouter API 的使用条款。

## 🤝 贡献

欢迎提交问题报告和功能建议。如需贡献代码，请先创建issue讨论。

---

**注意**: 使用前请确保已正确配置 OpenRouter API 密钥，并了解相关的使用费用。
