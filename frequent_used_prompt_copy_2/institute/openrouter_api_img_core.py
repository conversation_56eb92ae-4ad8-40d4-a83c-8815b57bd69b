# -*- coding: utf-8 -*-
"""
OpenRouter API图像处理核心架构 - 精简版
包含：图像编码、API调用、错误处理
"""
import requests
import json
import base64
import os
from typing import Optional, Dict, Any

# --- 配置 ---
OPENROUTER_URL = "https://openrouter.ai/api/v1/chat/completions"
API_KEY = "YOUR_OPENROUTER_API_KEY_HERE"
DEFAULT_MODEL = "google/gemini-2.5-pro"

# --- 核心图像处理类 ---
class OpenRouterImageProcessor:
    """OpenRouter图像处理器"""
    
    def __init__(self, api_key: str = None, model: str = DEFAULT_MODEL):
        self.api_key = api_key or API_KEY
        self.model = model
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def encode_image_to_base64(self, image_path: str) -> str:
        """将图像编码为base64"""
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图像文件不存在: {image_path}")
        
        with open(image_path, "rb") as image_file:
            encoded = base64.b64encode(image_file.read()).decode('utf-8')
            print(f"✅ 图像编码完成: {os.path.basename(image_path)}")
            return encoded
    
    def create_image_message(self, image_path: str, text_prompt: str, image_type: str = "jpeg") -> Dict[str, Any]:
        """创建包含图像的消息"""
        base64_image = self.encode_image_to_base64(image_path)
        data_url = f"data:image/{image_type};base64,{base64_image}"
        
        return {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": text_prompt
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": data_url
                    }
                }
            ]
        }
    
    def process_image(self, image_path: str, prompt: str = "描述这张图片", **kwargs) -> Optional[Dict[str, Any]]:
        """处理单张图像"""
        try:
            # 创建消息
            message = self.create_image_message(image_path, prompt)
            
            # 构建请求负载
            payload = {
                "model": self.model,
                "messages": [message],
                **kwargs  # 允许传入额外参数如temperature等
            }
            
            # 发送请求
            response = requests.post(OPENROUTER_URL, headers=self.headers, json=payload)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 图像处理成功: {os.path.basename(image_path)}")
                return result
            else:
                print(f"❌ 请求失败，状态码：{response.status_code}")
                print(f"错误信息：{response.text}")
                return None
                
        except FileNotFoundError as e:
            print(f"❌ 文件错误: {e}")
            return None
        except Exception as e:
            print(f"❌ 处理错误: {str(e)}")
            return None
    
    def batch_process_images(self, image_paths: list, prompt: str = "描述这张图片") -> Dict[str, Any]:
        """批量处理图像"""
        results = {}
        
        for image_path in image_paths:
            print(f"\n处理图像: {os.path.basename(image_path)}")
            result = self.process_image(image_path, prompt)
            results[image_path] = result
        
        return results
    
    def extract_response_content(self, api_response: Dict[str, Any]) -> Optional[str]:
        """提取API响应中的内容"""
        try:
            if "choices" in api_response and len(api_response["choices"]) > 0:
                choice = api_response["choices"][0]
                if "message" in choice and "content" in choice["message"]:
                    return choice["message"]["content"]
            return None
        except Exception as e:
            print(f"❌ 提取响应内容失败: {e}")
            return None

# --- 图像工具函数 ---
class ImageUtils:
    """图像处理工具函数"""
    
    @staticmethod
    def get_image_info(image_path: str) -> Optional[Dict[str, Any]]:
        """获取图像文件信息"""
        if not os.path.exists(image_path):
            return None
        
        file_size = os.path.getsize(image_path)
        file_ext = os.path.splitext(image_path)[1].lower()
        
        return {
            'path': image_path,
            'size_mb': file_size / (1024 * 1024),
            'size_bytes': file_size,
            'extension': file_ext,
            'exists': True
        }
    
    @staticmethod
    def validate_image_file(image_path: str) -> bool:
        """验证图像文件"""
        if not os.path.exists(image_path):
            print(f"❌ 图像文件不存在: {image_path}")
            return False
        
        valid_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
        file_ext = os.path.splitext(image_path)[1].lower()
        
        if file_ext not in valid_extensions:
            print(f"❌ 不支持的图像格式: {file_ext}")
            return False
        
        return True
    
    @staticmethod
    def get_image_type_from_path(image_path: str) -> str:
        """从文件路径获取图像类型"""
        ext = os.path.splitext(image_path)[1].lower()
        type_mapping = {
            '.jpg': 'jpeg',
            '.jpeg': 'jpeg',
            '.png': 'png',
            '.gif': 'gif',
            '.bmp': 'bmp',
            '.webp': 'webp'
        }
        return type_mapping.get(ext, 'jpeg')

# --- 电商翻译系统 ---
class EcommerceTranslationSystem:
    """电商翻译系统 - 专门用于鞋类产品翻译"""

    # 语言特定模型配置
    LANGUAGE_MODEL_MAPPING = {
        'german': "openai/chatgpt-4o-latest",
        'spanish': "openai/chatgpt-4o-latest",
        'italian': "openai/chatgpt-4o-latest",
        'french': "google/gemini-2.5-pro"
    }

    # 翻译提示模板
    TRANSLATION_PROMPT_TEMPLATE = """E-commerce Translation Task for Footwear Category:
Translate the following {origin_language} text into {target_language} for Amazon marketplace listings.

## Translation Requirements:
### Language Quality Standards:
- Provide natural, native-level {target_language} translation
- Use terminology appropriate for footwear/fashion industry
- Follow {target_language} e-commerce conventions and consumer expectations
- Ensure grammatical accuracy and proper spelling
- Match the tone and style suitable for product descriptions

### Content Preservation:
- Maintain all original meaning, technical specifications, and selling points
- Preserve product features, benefits, and key attributes
- Keep measurement units, sizes, and technical terms accurate
- Retain brand positioning and marketing appeal

### E-commerce Optimization:
- Use consumer-friendly language that drives purchasing decisions
- Employ terminology familiar to {target_language} online shoppers
- Ensure translation supports product searchability and conversion
- Avoid overly technical or medical terminology unless product-specific

### Universal Quality Standards:
- Avoid literal translations that sound unnatural to native speakers
- Use appropriate register for commercial/e-commerce context (avoid overly formal, literary, or technical language unless product-specific)
- Ensure grammatical accuracy including:
  * Gender/case agreement where applicable
  * Correct preposition usage
  * Proper spelling and spacing
  * Appropriate punctuation and capitalization
- Select terminology appropriate for the product category (avoid medical/religious terms for everyday products)
- Match cultural expectations and shopping behavior patterns of target market

### Language-Specific Refinements:
**German:** Standard commercial register; "Komfort" not "Linderung" for comfort features; proper compound noun capitalization
**French:** Gender agreement (beau/belle); natural contractions (du, au); e-commerce appropriate formality level
**English:** American spelling for US market; natural phrasing over literal translation; industry-standard terminology

### Output Format:
Provide only the translated text without explanations, comments, or additional formatting.

Text to translate:
###
{text}
###"""

    def __init__(self, api_key: str = None):
        self.api_key = api_key or API_KEY
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

    def get_model_for_language(self, target_language: str) -> str:
        """根据目标语言获取对应的模型"""
        language_key = target_language.lower()
        return self.LANGUAGE_MODEL_MAPPING.get(language_key, DEFAULT_MODEL)

    def create_translation_prompt(self, text: str, origin_language: str = "English",
                                target_language: str = "German") -> str:
        """创建翻译提示"""
        return self.TRANSLATION_PROMPT_TEMPLATE.format(
            origin_language=origin_language,
            target_language=target_language,
            text=text
        )

    def translate_text(self, text: str, target_language: str = "German",
                      origin_language: str = "English", **kwargs) -> Optional[Dict[str, Any]]:
        """翻译文本"""
        try:
            # 获取对应语言的模型
            model = self.get_model_for_language(target_language)

            # 创建翻译提示
            prompt = self.create_translation_prompt(text, origin_language, target_language)

            # 构建请求负载
            payload = {
                "model": model,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": kwargs.get("temperature", 0.3),  # 翻译任务使用较低温度
                **{k: v for k, v in kwargs.items() if k != "temperature"}
            }

            # 发送请求
            response = requests.post(OPENROUTER_URL, headers=self.headers, json=payload)

            if response.status_code == 200:
                result = response.json()
                print(f"✅ 翻译成功: {origin_language} -> {target_language} (模型: {model})")
                return result
            else:
                print(f"❌ 翻译请求失败，状态码：{response.status_code}")
                print(f"错误信息：{response.text}")
                return None

        except Exception as e:
            print(f"❌ 翻译错误: {str(e)}")
            return None

    def batch_translate(self, text: str, target_languages: list = None,
                       origin_language: str = "English") -> Dict[str, Any]:
        """批量翻译到多种语言"""
        if target_languages is None:
            target_languages = ["German", "French", "Italian", "Spanish"]

        results = {}

        for target_lang in target_languages:
            print(f"\n翻译到 {target_lang}...")
            result = self.translate_text(text, target_lang, origin_language)
            results[target_lang] = result

        return results

    def extract_translation_content(self, api_response: Dict[str, Any]) -> Optional[str]:
        """提取翻译结果内容"""
        try:
            if "choices" in api_response and len(api_response["choices"]) > 0:
                choice = api_response["choices"][0]
                if "message" in choice and "content" in choice["message"]:
                    return choice["message"]["content"].strip()
            return None
        except Exception as e:
            print(f"❌ 提取翻译内容失败: {e}")
            return None

# --- 预设提示模板 ---
class PromptTemplates:
    """常用提示模板"""

    DESCRIBE_IMAGE = "详细描述这张图片的内容。"
    GENERATE_PROMPT = "为这张图片生成一个详细的AI绘画提示词。"
    ANALYZE_COMPOSITION = "分析这张图片的构图、色彩和视觉元素。"
    EXTRACT_TEXT = "提取图片中的所有文字内容。"
    IDENTIFY_OBJECTS = "识别图片中的所有物体和元素。"

    @classmethod
    def custom_prompt(cls, task: str, details: str = "") -> str:
        """创建自定义提示"""
        base = f"请{task}这张图片"
        if details:
            base += f"，{details}"
        return base + "。"

# --- 使用示例 ---
def example_usage():
    """使用示例"""
    # 初始化处理器
    processor = OpenRouterImageProcessor(
        api_key=API_KEY,
        model="google/gemini-2.5-pro"
    )

    # 单张图像处理
    image_path = "/path/to/your/image.jpg"

    if ImageUtils.validate_image_file(image_path):
        # 获取图像信息
        image_info = ImageUtils.get_image_info(image_path)
        print(f"图像信息: {image_info}")

        # 处理图像
        result = processor.process_image(
            image_path,
            PromptTemplates.GENERATE_PROMPT,
            temperature=0.7
        )

        if result:
            content = processor.extract_response_content(result)
            if content:
                print(f"\n=== 分析结果 ===\n{content}")

    # 批量处理示例
    image_files = [
        "/path/to/image1.jpg",
        "/path/to/image2.png"
    ]

    valid_images = [img for img in image_files if ImageUtils.validate_image_file(img)]
    if valid_images:
        batch_results = processor.batch_process_images(valid_images, PromptTemplates.DESCRIBE_IMAGE)

        for image_path, result in batch_results.items():
            if result:
                content = processor.extract_response_content(result)
                print(f"\n{os.path.basename(image_path)}: {content[:100]}...")

def translation_example_usage():
    """翻译系统使用示例"""
    # 初始化翻译系统
    translator = EcommerceTranslationSystem(api_key=API_KEY)

    # 示例产品描述文本
    sample_text = """
    Premium Running Shoes with Advanced Cushioning Technology

    Experience ultimate comfort with our revolutionary running shoes featuring:
    - Ultra-lightweight breathable mesh upper
    - Advanced foam midsole for superior shock absorption
    - Durable rubber outsole with enhanced grip pattern
    - Ergonomic design for natural foot movement
    - Available in sizes 6-12 for men and women
    - Perfect for daily training, jogging, and casual wear

    Key Features:
    • Moisture-wicking interior lining
    • Reinforced heel counter for stability
    • Flexible forefoot design
    • Non-slip rubber sole
    • Machine washable materials

    Ideal for runners seeking performance, comfort, and style in one premium package.
    """

    print("=== 电商翻译系统示例 ===\n")

    # 单语言翻译示例
    print("1. 单语言翻译 (英语 -> 德语):")
    german_result = translator.translate_text(
        text=sample_text,
        target_language="German",
        origin_language="English"
    )

    if german_result:
        german_translation = translator.extract_translation_content(german_result)
        if german_translation:
            print(f"德语翻译:\n{german_translation}\n")

    # 批量翻译示例
    print("2. 批量翻译到多种语言:")
    batch_results = translator.batch_translate(
        text=sample_text,
        target_languages=["German", "French", "Spanish", "Italian"],
        origin_language="English"
    )

    print("\n=== 批量翻译结果 ===")
    for language, result in batch_results.items():
        if result:
            translation = translator.extract_translation_content(result)
            if translation:
                print(f"\n{language}:")
                print(f"{translation[:200]}..." if len(translation) > 200 else translation)
                print("-" * 50)

    # 显示模型分配信息
    print("\n=== 语言模型分配 ===")
    for lang in ["German", "French", "Spanish", "Italian"]:
        model = translator.get_model_for_language(lang)
        print(f"{lang}: {model}")

def combined_example_usage():
    """综合使用示例 - 图像处理 + 翻译"""
    print("=== 综合使用示例：图像分析 + 翻译 ===\n")

    # 初始化两个系统
    image_processor = OpenRouterImageProcessor(api_key=API_KEY)
    translator = EcommerceTranslationSystem(api_key=API_KEY)

    # 假设我们有一张鞋子的产品图片
    image_path = "/path/to/shoe_product.jpg"

    if ImageUtils.validate_image_file(image_path):
        # 1. 首先分析图像，生成产品描述
        print("步骤1: 分析产品图像...")
        analysis_prompt = "Analyze this footwear product image and create a detailed e-commerce product description in English, focusing on visible features, style, materials, and selling points suitable for Amazon marketplace."

        analysis_result = image_processor.process_image(
            image_path,
            analysis_prompt,
            temperature=0.5
        )

        if analysis_result:
            english_description = image_processor.extract_response_content(analysis_result)
            if english_description:
                print(f"英语产品描述:\n{english_description}\n")

                # 2. 将生成的描述翻译成多种语言
                print("步骤2: 翻译产品描述...")
                translations = translator.batch_translate(
                    text=english_description,
                    target_languages=["German", "French", "Spanish"],
                    origin_language="English"
                )

                print("\n=== 多语言产品描述 ===")
                for language, result in translations.items():
                    if result:
                        translation = translator.extract_translation_content(result)
                        if translation:
                            print(f"\n{language}:")
                            print(translation)
                            print("-" * 60)
    else:
        print("请提供有效的产品图像路径来运行综合示例")

# --- 配置验证 ---
def validate_config():
    """验证配置"""
    if API_KEY == "YOUR_OPENROUTER_API_KEY_HERE":
        print("⚠️ 请先设置OpenRouter API密钥")
        return False
    return True

if __name__ == "__main__":
    if validate_config():
        print("选择运行模式:")
        print("1. 图像处理示例")
        print("2. 翻译系统示例")
        print("3. 综合示例 (图像分析 + 翻译)")
        print("4. 运行所有示例")

        try:
            choice = input("\n请输入选择 (1-4): ").strip()

            if choice == "1":
                example_usage()
            elif choice == "2":
                translation_example_usage()
            elif choice == "3":
                combined_example_usage()
            elif choice == "4":
                print("\n=== 运行所有示例 ===")
                example_usage()
                print("\n" + "="*60 + "\n")
                translation_example_usage()
                print("\n" + "="*60 + "\n")
                combined_example_usage()
            else:
                print("无效选择，运行默认图像处理示例")
                example_usage()

        except KeyboardInterrupt:
            print("\n程序已取消")
        except Exception as e:
            print(f"运行错误: {e}")
    else:
        print("配置验证失败，请检查API密钥设置")
