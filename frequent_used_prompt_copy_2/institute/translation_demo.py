#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电商翻译系统演示脚本
展示如何使用翻译系统进行鞋类产品描述的多语言翻译
"""

import sys
import os
import json
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入翻译系统和配置
from openrouter_api_img_core import EcommerceTranslationSystem
from ecommerce_translation_config import (
    TranslationConfig, 
    FootwearTranslationConfig,
    TranslationQualityChecker,
    SAMPLE_PRODUCT_DESCRIPTIONS
)

class TranslationDemo:
    """翻译系统演示类"""
    
    def __init__(self, api_key: str = None):
        self.translator = EcommerceTranslationSystem(api_key=api_key)
        self.config = TranslationConfig()
        self.footwear_config = FootwearTranslationConfig()
        self.quality_checker = TranslationQualityChecker()
    
    def demo_single_translation(self):
        """演示单语言翻译"""
        print("=== 单语言翻译演示 ===\n")
        
        # 使用示例产品描述
        sample_text = SAMPLE_PRODUCT_DESCRIPTIONS['running_shoes']
        target_language = "German"
        
        print(f"原文 (English):")
        print(sample_text)
        print("\n" + "-"*60 + "\n")
        
        # 执行翻译
        print(f"翻译到 {target_language}...")
        result = self.translator.translate_text(
            text=sample_text,
            target_language=target_language,
            origin_language="English"
        )
        
        if result:
            translation = self.translator.extract_translation_content(result)
            if translation:
                print(f"{target_language} 翻译:")
                print(translation)
                
                # 质量检查
                if target_language.lower() == "german":
                    quality_report = self.quality_checker.check_german_quality(translation)
                    print(f"\n质量评估: {quality_report['score']}/100")
                    if quality_report['issues']:
                        print("发现问题:", quality_report['issues'])
                    if quality_report['suggestions']:
                        print("改进建议:", quality_report['suggestions'])
        
        print("\n" + "="*80 + "\n")
    
    def demo_batch_translation(self):
        """演示批量翻译"""
        print("=== 批量翻译演示 ===\n")
        
        # 使用休闲运动鞋描述
        sample_text = SAMPLE_PRODUCT_DESCRIPTIONS['casual_sneakers']
        target_languages = ["German", "French", "Spanish", "Italian"]
        
        print("原文 (English):")
        print(sample_text)
        print("\n" + "-"*60 + "\n")
        
        # 执行批量翻译
        print("开始批量翻译...")
        batch_results = self.translator.batch_translate(
            text=sample_text,
            target_languages=target_languages,
            origin_language="English"
        )
        
        print("\n=== 批量翻译结果 ===")
        for language, result in batch_results.items():
            if result:
                translation = self.translator.extract_translation_content(result)
                if translation:
                    model_used = self.translator.get_model_for_language(language)
                    print(f"\n{language} (模型: {model_used}):")
                    print(translation)
                    print("-" * 50)
        
        print("\n" + "="*80 + "\n")
    
    def demo_quality_assessment(self):
        """演示翻译质量评估"""
        print("=== 翻译质量评估演示 ===\n")
        
        # 使用登山靴描述进行德语翻译
        sample_text = SAMPLE_PRODUCT_DESCRIPTIONS['hiking_boots']
        
        print("原文:")
        print(sample_text[:200] + "...")
        print("\n" + "-"*60 + "\n")
        
        # 翻译到德语
        result = self.translator.translate_text(
            text=sample_text,
            target_language="German",
            origin_language="English"
        )
        
        if result:
            translation = self.translator.extract_translation_content(result)
            if translation:
                print("德语翻译:")
                print(translation)
                print("\n" + "-"*40 + "\n")
                
                # 进行质量评估
                print("=== 质量评估报告 ===")
                
                # 德语特定质量检查
                german_quality = self.quality_checker.check_german_quality(translation)
                print(f"德语质量评分: {german_quality['score']}/100")
                if german_quality['issues']:
                    print("发现的问题:")
                    for issue in german_quality['issues']:
                        print(f"  - {issue}")
                if german_quality['suggestions']:
                    print("改进建议:")
                    for suggestion in german_quality['suggestions']:
                        print(f"  - {suggestion}")
                
                # 完整性检查
                completeness = self.quality_checker.check_translation_completeness(
                    sample_text, translation
                )
                print(f"\n完整性评分: {completeness['completeness_score']}/100")
                print(f"原文词数: {completeness['original_words']}")
                print(f"译文词数: {completeness['translation_words']}")
                print(f"长度比例: {completeness['length_ratio']:.2f}")
                
                if completeness['issues']:
                    print("完整性问题:")
                    for issue in completeness['issues']:
                        print(f"  - {issue}")
        
        print("\n" + "="*80 + "\n")
    
    def demo_model_assignment(self):
        """演示模型分配策略"""
        print("=== 模型分配策略演示 ===\n")
        
        print("语言特定模型分配:")
        for language in ["German", "French", "Spanish", "Italian"]:
            model = self.translator.get_model_for_language(language)
            print(f"{language:10} -> {model}")
        
        print(f"\n默认模型: {self.translator.get_model_for_language('unknown_language')}")
        
        print("\n模型选择原因:")
        print("• German, Spanish, Italian: 使用 ChatGPT-4o-latest")
        print("  - 在这些语言上表现优异")
        print("  - 更好的语法准确性和自然度")
        print("• French: 使用 Gemini-2.5-pro") 
        print("  - 在法语翻译上有特殊优势")
        print("  - 更好的语言文化适应性")
        
        print("\n" + "="*80 + "\n")
    
    def save_translation_results(self, results: dict, filename: str = None):
        """保存翻译结果到文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"translation_results_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"✅ 翻译结果已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存失败: {e}")
    
    def run_full_demo(self):
        """运行完整演示"""
        print("🚀 电商翻译系统完整演示")
        print("="*80)
        
        try:
            # 1. 单语言翻译演示
            self.demo_single_translation()
            
            # 2. 批量翻译演示  
            self.demo_batch_translation()
            
            # 3. 质量评估演示
            self.demo_quality_assessment()
            
            # 4. 模型分配演示
            self.demo_model_assignment()
            
            print("✅ 演示完成！")
            
        except KeyboardInterrupt:
            print("\n❌ 演示被用户中断")
        except Exception as e:
            print(f"❌ 演示过程中出现错误: {e}")

def main():
    """主函数"""
    print("电商翻译系统演示")
    print("请确保已设置正确的 OpenRouter API 密钥")
    
    # 检查API密钥
    api_key = os.getenv('OPENROUTER_API_KEY')
    if not api_key:
        print("⚠️ 未找到环境变量 OPENROUTER_API_KEY")
        print("请设置API密钥或在代码中直接指定")
        api_key = input("请输入您的 OpenRouter API 密钥 (或按回车跳过): ").strip()
        if not api_key:
            print("使用默认配置运行演示...")
    
    # 创建演示实例
    demo = TranslationDemo(api_key=api_key)
    
    # 运行演示
    demo.run_full_demo()

if __name__ == "__main__":
    main()
