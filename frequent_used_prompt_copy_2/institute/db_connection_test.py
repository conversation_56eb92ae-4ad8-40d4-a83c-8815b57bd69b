#!/usr/bin/env python3
import pymysql
import sys

def test_connection(host, port, user, password, db, env_name):
    """测试数据库连接并执行简单查询"""
    print(f"\n正在测试 {env_name} 环境的数据库连接...")
    print(f"连接信息: {host}:{port}, 用户: {user}, 数据库: {db}")
    
    try:
        # 建立连接
        conn = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=db,
            charset='utf8mb4'
        )
        
        # 创建游标
        cursor = conn.cursor()
        
        # 执行简单查询
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        
        # 关闭连接
        cursor.close()
        conn.close()
        
        print(f"✅ {env_name} 环境连接成功! 查询结果: {result}")
        return True
    
    except Exception as e:
        print(f"❌ {env_name} 环境连接失败: {str(e)}")
        return False

def main():
    # 测试环境连接信息
    test_config = {
        "host": "java_mysql_wan.uat.wangoon.cn",
        "port": 3306,
        "user": "aigc_con",
        "password": "BLG@y$x4WCdWhvx",
        "db": "aigc"
    }
    
    # 生产环境连接信息
    prod_config = {
        "host": "java_mysql_wan.pro.wangoon.cn", 
        "port": 3306,
        "user": "dbuser_aigcidea",
        "password": "*Eq098MCGGuy0L5",
        "db": "aigc_idea"
    }
    
    # 测试连接
    test_success = test_connection(**test_config, env_name="测试")
    prod_success = test_connection(**prod_config, env_name="生产")
    
    # 总结
    print("\n连接测试结果:")
    print(f"测试环境: {'成功' if test_success else '失败'}")
    print(f"生产环境: {'成功' if prod_success else '失败'}")

if __name__ == "__main__":
    print("数据库连接测试开始...")
    main()
    print("数据库连接测试结束") 