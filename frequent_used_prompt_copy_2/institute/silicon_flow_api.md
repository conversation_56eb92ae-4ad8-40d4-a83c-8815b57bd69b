api_key:"sk-iinwlldkziyjemchdrzmwuppvmjcdjemdxhjlompizouxbnl"

model: deepseek-ai/DeepSeek-V3

api的调度代码如下：
```
import requests

url = "https://api.siliconflow.cn/v1/chat/completions"

payload = {
    "model": "deepseek-ai/DeepSeek-V3",
    "messages": [
        {
            "role": "user",
            "content": "What opportunities and challenges will the Chinese large model industry face in 2025?"
        }
    ]
}
headers = {
    "Authorization": "Bearer <token>",
    "Content-Type": "application/json"
}

response = requests.request("POST", url, json=payload, headers=headers)

print(response.text)
``` 