# API调度策略精华集合

## 📁 文件夹结构

```
frequent_used_prompt copy/
├── README.md                    # 本文件，总体说明
├── text/                        # 文本类API调度
│   ├── 文字描述型/              # 纯文字描述版本
│   │   ├── SiliconFlow_API概述.md
│   │   └── DeepSeek_API概述.md
│   └── 代码实现型/              # 文字描述+完整代码版本
│       ├── SiliconFlow_API.md
│       └── DeepSeek_API.md
├── images/                      # 图片类API调度
│   ├── 文字描述型/              # 纯文字描述版本
│   │   └── OpenRouter_API概述.md
│   └── 代码实现型/              # 文字描述+完整代码版本
│       └── OpenRouter_API.md
├── video/                       # 视频类API调度
│   ├── 文字描述型/              # 纯文字描述版本
│   │   └── Google_Gemini_API概述.md
│   └── 代码实现型/              # 文字描述+完整代码版本
│       └── Google_Gemini_API.md
└── 整体api调度策略/              # 通用调度策略
    ├── 文字描述型/              # 纯文字描述版本
    │   ├── 必备策略概述.md
    │   └── 可选策略概述.md
    └── 代码实现型/              # 文字描述+完整代码版本
        ├── 必备策略.md
        └── 可选策略.md
```

## 🎯 使用指南

### 1. 选择合适的版本类型

#### 📖 文字描述型
- **适用人群**: 产品经理、架构师、初学者
- **内容特点**: 纯文字描述，重点讲解原理和策略
- **使用场景**: 理解概念、制定方案、学习原理
- **优势**: 易读易懂，快速掌握核心思想

#### 💻 代码实现型
- **适用人群**: 开发工程师、技术专家
- **内容特点**: 详细代码实现+文字说明
- **使用场景**: 具体开发、技术实现、生产部署
- **优势**: 即用即拿，完整的实现方案

### 2. 按媒体类型选择API

#### 📝 文本处理
- **SiliconFlow API**: 适合大规模文本生成、分析、翻译
- **DeepSeek API**: 适合代码生成、推理任务、数学问题

#### 🖼️ 图片处理
- **OpenRouter API**: 适合图片描述、OCR、视觉问答

#### 🎬 视频处理
- **Google Gemini API**: 适合视频摘要、内容分析、转录

### 3. 核心策略应用

#### 必备策略（所有API都需要）
1. **令牌桶限流** - 控制请求频率
2. **智能重试** - 处理各种错误情况
3. **性能监控** - 实时跟踪API健康状况
4. **统一接口** - 提供一致的调用体验

#### 可选策略（根据需求选择）
1. **自适应并发控制** - 动态调整并发数
2. **优先级队列** - 重要任务优先处理
3. **负载均衡** - 多API提供商分配
4. **智能缓存** - 减少重复请求

## 🚀 快速开始

### 文字描述型使用示例

```markdown
# 查看API概述，了解基本特点和调度策略
1. 阅读 text/文字描述型/SiliconFlow_API概述.md
2. 了解限流策略：30-60次/分钟，支持3-5线程并发
3. 掌握重试机制：最多5次重试，指数退避策略
4. 理解功能分类：文本生成、分析、转换、问答四大类

# 制定调度方案
- 根据业务需求选择合适的temperature参数
- 设计合理的限流和重试参数
- 规划性能监控和预警机制
```

### 代码实现型使用示例

```python
# 1. 导入必备策略（完整代码实现）
from 整体api调度策略.代码实现型.必备策略 import TokenBucketLimiter, smart_retry, PerformanceMonitor

# 2. 选择具体API（完整代码实现）
from text.代码实现型.SiliconFlow_API import SiliconFlowAPI

# 3. 创建调度器
limiter = TokenBucketLimiter(tokens_per_minute=30)
monitor = PerformanceMonitor()
api = SiliconFlowAPI()

# 4. 处理请求
@smart_retry
def process_text(content):
    limiter.wait_for_token()
    start_time = monitor.record_request_start()

    try:
        response = api.call_api(content)
        monitor.record_request_complete(start_time, success=True)
        return response
    except Exception as e:
        monitor.record_request_complete(start_time, success=False)
        raise

# 5. 调用
result = process_text("你好，世界！")
print("结果:", api.extract_content(result))
print("统计:", monitor.get_metrics())
```

### 高级策略使用示例

```python
# 使用可选策略（完整代码实现）
from 整体api调度策略.代码实现型.可选策略 import AdvancedAPIScheduler, Priority

# 创建高级调度器
scheduler = AdvancedAPIScheduler(
    providers=["siliconflow", "openrouter"],
    cache_enabled=True,
    load_balancing=True
)

# 处理不同优先级的请求
high_priority_result = scheduler.process_request(
    "紧急任务：分析这个文本",
    priority=Priority.HIGH
)

normal_result = scheduler.process_request(
    "普通任务：翻译这段话",
    priority=Priority.NORMAL,
    use_cache=True
)

# 查看综合统计
stats = scheduler.get_comprehensive_stats()
```

## 📊 性能对比

| API类型 | 推荐并发数 | 限流频率 | 平均响应时间 | 适用场景 |
|---------|------------|----------|--------------|----------|
| 文本API | 3-5线程 | 30-60次/分钟 | 1-5秒 | 大量文本处理 |
| 图片API | 2-3线程 | 15-20次/分钟 | 5-15秒 | 图片理解分析 |
| 视频API | 1线程 | 8-10次/分钟 | 15-60秒 | 视频内容分析 |

## 🔧 配置管理

### 环境变量设置

```bash
# 文本API
export SILICONFLOW_API_TOKEN="your_siliconflow_token"
export DEEPSEEK_API_KEY="your_deepseek_key"

# 图片API
export OPENROUTER_API_KEY="your_openrouter_key"

# 视频API
export GOOGLE_API_KEY="your_google_key"
```

### 配置文件示例

```python
# config.py
API_CONFIGS = {
    "text": {
        "siliconflow": {
            "rate_limit": 30,
            "max_concurrent": 5,
            "timeout": 240
        },
        "deepseek": {
            "rate_limit": 60,
            "max_concurrent": 10,
            "timeout": 300
        }
    },
    "image": {
        "openrouter": {
            "rate_limit": 15,
            "max_concurrent": 3,
            "timeout": 300
        }
    },
    "video": {
        "google": {
            "rate_limit": 8,
            "max_concurrent": 1,
            "timeout": 600
        }
    }
}
```

## 🎯 最佳实践

### 1. 选择策略原则
- **文本任务**: 优先使用必备策略 + 缓存
- **图片任务**: 必备策略 + 预处理 + 适度并发
- **视频任务**: 必备策略 + 严格限流 + 单线程

### 2. 错误处理
- 区分不同错误类型
- 实现合适的重试策略
- 记录详细的错误日志
- 提供优雅降级方案

### 3. 性能优化
- 根据API特性调整参数
- 监控关键性能指标
- 定期清理缓存和日志
- 使用连接池复用连接

### 4. 成本控制
- 设置合理的限流参数
- 使用缓存减少重复请求
- 监控token使用量
- 选择性使用高级策略

## 📋 故障排查

### 常见问题

1. **限流错误 (429)**
   - 检查限流器配置
   - 调整请求频率
   - 实现更长的退避时间

2. **超时错误**
   - 增加timeout设置
   - 检查网络连接
   - 考虑使用更快的API

3. **配额耗尽**
   - 监控API使用量
   - 实现配额预警
   - 准备备用API

4. **性能下降**
   - 检查并发数设置
   - 分析响应时间趋势
   - 考虑启用缓存

### 监控指标

- **成功率**: > 95%
- **平均响应时间**: 文本<5s, 图片<15s, 视频<60s
- **QPS**: 根据限流设置
- **错误率**: < 5%

## 🔄 更新日志

- **v1.0**: 初始版本，包含基础API调度功能
- **v1.1**: 添加必备策略和可选策略
- **v1.2**: 完善文档和使用示例
- **v1.3**: 添加故障排查和最佳实践
- **v1.4**: 清理不必要文件，保持结构简洁
- **v2.0**: 重大更新，增加双层级结构（文字描述型+代码实现型）

## 📞 技术支持

如有问题或建议，请参考：
1. 各API文档中的使用示例
2. 必备策略和可选策略说明
3. 最佳实践指南
4. 故障排查手册

这套API调度策略集合提供了从基础到高级的完整解决方案，可以根据具体需求灵活组合使用。
