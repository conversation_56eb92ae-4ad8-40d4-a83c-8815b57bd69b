# API调度可选策略

## 🎯 一、自适应并发控制

### 1. 动态并发数调整
```python
import threading
import time
from collections import deque

class AdaptiveConcurrencyController:
    """自适应并发控制器"""
    
    def __init__(self, initial_concurrency=3, min_concurrency=1, max_concurrency=10):
        self.current_concurrency = initial_concurrency
        self.min_concurrency = min_concurrency
        self.max_concurrency = max_concurrency
        
        # AIMD参数 (加性增加，乘性减少)
        self.additive_increase = 1
        self.multiplicative_decrease = 0.7
        
        # 性能指标窗口
        self.window_size = 20
        self.response_times = deque(maxlen=self.window_size)
        self.success_rates = deque(maxlen=self.window_size)
        
        self.lock = threading.Lock()
        self.last_adjustment_time = time.time()
        self.adjustment_interval = 30  # 30秒调整一次
    
    def record_performance(self, response_time, success):
        """记录性能指标"""
        with self.lock:
            self.response_times.append(response_time)
            self.success_rates.append(1.0 if success else 0.0)
    
    def should_adjust(self):
        """判断是否应该调整并发数"""
        return (time.time() - self.last_adjustment_time >= self.adjustment_interval and
                len(self.response_times) >= self.window_size // 2)
    
    def adjust_concurrency(self):
        """调整并发数"""
        if not self.should_adjust():
            return self.current_concurrency
        
        with self.lock:
            # 计算平均性能指标
            avg_response_time = sum(self.response_times) / len(self.response_times)
            avg_success_rate = sum(self.success_rates) / len(self.success_rates)
            
            old_concurrency = self.current_concurrency
            
            # 调整策略
            if avg_success_rate < 0.8:
                # 成功率低，大幅降低并发
                adjustment = -max(1, int(self.current_concurrency * (1 - self.multiplicative_decrease)))
            elif avg_response_time > 5.0:
                # 响应时间长，小幅降低并发
                adjustment = -1
            elif avg_success_rate >= 0.95 and avg_response_time <= 2.0:
                # 性能良好，增加并发
                adjustment = self.additive_increase
            else:
                adjustment = 0
            
            # 应用调整
            self.current_concurrency = max(
                self.min_concurrency,
                min(self.max_concurrency, self.current_concurrency + adjustment)
            )
            
            self.last_adjustment_time = time.time()
            
            if adjustment != 0:
                print(f"🎛️ 并发数调整: {old_concurrency} -> {self.current_concurrency} "
                      f"(成功率: {avg_success_rate:.2%}, 响应时间: {avg_response_time:.2f}s)")
            
            return self.current_concurrency
    
    def get_current_concurrency(self):
        """获取当前并发数"""
        return self.current_concurrency
    
    def get_stats(self):
        """获取统计信息"""
        with self.lock:
            if not self.response_times:
                return {
                    'current_concurrency': self.current_concurrency,
                    'avg_response_time': 0,
                    'avg_success_rate': 1.0,
                    'samples': 0
                }
            
            return {
                'current_concurrency': self.current_concurrency,
                'avg_response_time': sum(self.response_times) / len(self.response_times),
                'avg_success_rate': sum(self.success_rates) / len(self.success_rates),
                'samples': len(self.response_times)
            }
```

## 🔄 二、优先级队列调度

### 1. 任务优先级管理
```python
import heapq
import threading
from enum import IntEnum
from dataclasses import dataclass, field
from typing import Any, Callable

class Priority(IntEnum):
    """任务优先级"""
    CRITICAL = 1
    HIGH = 2
    NORMAL = 3
    LOW = 4

@dataclass
class Task:
    """任务数据结构"""
    priority: Priority
    func: Callable
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    retry_count: int = 0
    created_time: float = field(default_factory=time.time)
    task_id: str = field(default_factory=lambda: str(time.time()))
    
    def __lt__(self, other):
        # 优先级相同时，重试次数多的优先，然后按创建时间
        if self.priority == other.priority:
            if self.retry_count == other.retry_count:
                return self.created_time < other.created_time
            return self.retry_count > other.retry_count
        return self.priority < other.priority

class PriorityTaskQueue:
    """优先级任务队列"""
    
    def __init__(self, max_size=1000):
        self.max_size = max_size
        self.queue = []
        self.task_count = 0
        self.lock = threading.Lock()
        
        # 统计信息
        self.priority_counts = {p: 0 for p in Priority}
        self.completed_tasks = 0
        self.failed_tasks = 0
    
    def add_task(self, priority, func, *args, **kwargs):
        """添加任务"""
        with self.lock:
            if len(self.queue) >= self.max_size:
                # 队列满时，移除最低优先级的任务
                self._remove_lowest_priority_task()
            
            task = Task(priority, func, args, kwargs)
            heapq.heappush(self.queue, task)
            self.task_count += 1
            self.priority_counts[priority] += 1
            
            return task.task_id
    
    def get_task(self, timeout=None):
        """获取最高优先级任务"""
        start_time = time.time()
        
        while True:
            with self.lock:
                if self.queue:
                    task = heapq.heappop(self.queue)
                    self.priority_counts[task.priority] -= 1
                    return task
            
            if timeout and (time.time() - start_time) >= timeout:
                return None
            
            time.sleep(0.1)
    
    def requeue_task(self, task, increase_priority=True):
        """重新排队任务（用于重试）"""
        with self.lock:
            task.retry_count += 1
            
            # 可选：提高重试任务的优先级
            if increase_priority and task.priority > Priority.CRITICAL:
                task.priority = Priority(task.priority - 1)
            
            heapq.heappush(self.queue, task)
            self.priority_counts[task.priority] += 1
    
    def _remove_lowest_priority_task(self):
        """移除最低优先级任务"""
        if not self.queue:
            return
        
        # 找到最低优先级任务并移除
        lowest_priority_task = max(self.queue, key=lambda t: (t.priority, -t.retry_count, -t.created_time))
        self.queue.remove(lowest_priority_task)
        heapq.heapify(self.queue)
        self.priority_counts[lowest_priority_task.priority] -= 1
    
    def get_stats(self):
        """获取队列统计"""
        with self.lock:
            return {
                'queue_size': len(self.queue),
                'priority_counts': dict(self.priority_counts),
                'completed_tasks': self.completed_tasks,
                'failed_tasks': self.failed_tasks,
                'total_tasks': self.task_count
            }
```

## 📊 三、负载均衡策略

### 1. 多API提供商负载均衡
```python
import random
from collections import defaultdict
from typing import List, Dict

class LoadBalancer:
    """API负载均衡器"""
    
    def __init__(self, providers: List[str], strategy="weighted_round_robin"):
        self.providers = providers
        self.strategy = strategy
        
        # 权重配置（可根据API性能调整）
        self.weights = {provider: 1.0 for provider in providers}
        
        # 轮询状态
        self.round_robin_index = 0
        
        # 性能统计
        self.provider_stats = defaultdict(lambda: {
            'requests': 0,
            'successes': 0,
            'failures': 0,
            'avg_response_time': 0,
            'total_response_time': 0
        })
        
        self.lock = threading.Lock()
    
    def select_provider(self) -> str:
        """选择API提供商"""
        if self.strategy == "round_robin":
            return self._round_robin_select()
        elif self.strategy == "weighted_round_robin":
            return self._weighted_round_robin_select()
        elif self.strategy == "least_connections":
            return self._least_connections_select()
        elif self.strategy == "performance_based":
            return self._performance_based_select()
        else:
            return random.choice(self.providers)
    
    def _round_robin_select(self) -> str:
        """轮询选择"""
        with self.lock:
            provider = self.providers[self.round_robin_index]
            self.round_robin_index = (self.round_robin_index + 1) % len(self.providers)
            return provider
    
    def _weighted_round_robin_select(self) -> str:
        """加权轮询选择"""
        total_weight = sum(self.weights.values())
        random_weight = random.uniform(0, total_weight)
        
        current_weight = 0
        for provider in self.providers:
            current_weight += self.weights[provider]
            if random_weight <= current_weight:
                return provider
        
        return self.providers[-1]
    
    def _least_connections_select(self) -> str:
        """最少连接选择"""
        with self.lock:
            # 选择当前请求数最少的提供商
            min_requests = min(self.provider_stats[p]['requests'] for p in self.providers)
            candidates = [p for p in self.providers 
                         if self.provider_stats[p]['requests'] == min_requests]
            return random.choice(candidates)
    
    def _performance_based_select(self) -> str:
        """基于性能选择"""
        with self.lock:
            # 计算每个提供商的性能分数
            scores = {}
            for provider in self.providers:
                stats = self.provider_stats[provider]
                if stats['requests'] == 0:
                    scores[provider] = 1.0  # 新提供商给予最高分
                else:
                    success_rate = stats['successes'] / stats['requests']
                    avg_response_time = stats['avg_response_time']
                    # 性能分数 = 成功率 / (1 + 响应时间)
                    scores[provider] = success_rate / (1 + avg_response_time)
            
            # 按分数加权随机选择
            total_score = sum(scores.values())
            if total_score == 0:
                return random.choice(self.providers)
            
            random_score = random.uniform(0, total_score)
            current_score = 0
            for provider, score in scores.items():
                current_score += score
                if random_score <= current_score:
                    return provider
            
            return self.providers[-1]
    
    def record_request_start(self, provider: str):
        """记录请求开始"""
        with self.lock:
            self.provider_stats[provider]['requests'] += 1
    
    def record_request_complete(self, provider: str, success: bool, response_time: float):
        """记录请求完成"""
        with self.lock:
            stats = self.provider_stats[provider]
            
            if success:
                stats['successes'] += 1
            else:
                stats['failures'] += 1
            
            # 更新平均响应时间
            stats['total_response_time'] += response_time
            stats['avg_response_time'] = stats['total_response_time'] / stats['requests']
    
    def update_weights(self, weights: Dict[str, float]):
        """更新权重"""
        with self.lock:
            self.weights.update(weights)
    
    def get_stats(self):
        """获取统计信息"""
        with self.lock:
            return {
                'strategy': self.strategy,
                'providers': self.providers,
                'weights': dict(self.weights),
                'stats': dict(self.provider_stats)
            }
```

## 🔧 四、缓存策略

### 1. 智能缓存管理
```python
import hashlib
import json
import time
from typing import Any, Optional

class IntelligentCache:
    """智能缓存管理器"""
    
    def __init__(self, max_size=1000, default_ttl=3600):
        self.max_size = max_size
        self.default_ttl = default_ttl
        
        self.cache = {}
        self.access_times = {}
        self.hit_counts = {}
        
        self.lock = threading.Lock()
        
        # 统计信息
        self.hits = 0
        self.misses = 0
        self.evictions = 0
    
    def _generate_key(self, content: str, params: dict = None) -> str:
        """生成缓存键"""
        cache_data = {
            'content': content,
            'params': params or {}
        }
        cache_str = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_str.encode()).hexdigest()
    
    def get(self, content: str, params: dict = None) -> Optional[Any]:
        """获取缓存"""
        key = self._generate_key(content, params)
        
        with self.lock:
            if key not in self.cache:
                self.misses += 1
                return None
            
            # 检查是否过期
            cached_item = self.cache[key]
            if time.time() > cached_item['expires_at']:
                del self.cache[key]
                del self.access_times[key]
                del self.hit_counts[key]
                self.misses += 1
                return None
            
            # 更新访问信息
            self.access_times[key] = time.time()
            self.hit_counts[key] = self.hit_counts.get(key, 0) + 1
            self.hits += 1
            
            return cached_item['data']
    
    def set(self, content: str, data: Any, params: dict = None, ttl: int = None):
        """设置缓存"""
        key = self._generate_key(content, params)
        ttl = ttl or self.default_ttl
        
        with self.lock:
            # 如果缓存已满，执行LRU淘汰
            if len(self.cache) >= self.max_size and key not in self.cache:
                self._evict_lru()
            
            self.cache[key] = {
                'data': data,
                'created_at': time.time(),
                'expires_at': time.time() + ttl
            }
            self.access_times[key] = time.time()
            self.hit_counts[key] = 0
    
    def _evict_lru(self):
        """淘汰最近最少使用的项"""
        if not self.cache:
            return
        
        # 找到最近最少访问的键
        lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        
        del self.cache[lru_key]
        del self.access_times[lru_key]
        del self.hit_counts[lru_key]
        self.evictions += 1
    
    def clear_expired(self):
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = []
        
        with self.lock:
            for key, item in self.cache.items():
                if current_time > item['expires_at']:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.cache[key]
                del self.access_times[key]
                del self.hit_counts[key]
        
        return len(expired_keys)
    
    def get_stats(self):
        """获取缓存统计"""
        with self.lock:
            total_requests = self.hits + self.misses
            hit_rate = self.hits / total_requests if total_requests > 0 else 0
            
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'hits': self.hits,
                'misses': self.misses,
                'hit_rate': hit_rate,
                'evictions': self.evictions
            }
```

## 🎯 五、使用示例

### 完整的可选策略集成
```python
class AdvancedAPIScheduler:
    """高级API调度器"""
    
    def __init__(self, providers, cache_enabled=True, load_balancing=True):
        # 基础组件
        self.providers = providers
        
        # 可选组件
        self.concurrency_controller = AdaptiveConcurrencyController() if True else None
        self.task_queue = PriorityTaskQueue() if True else None
        self.load_balancer = LoadBalancer(providers) if load_balancing else None
        self.cache = IntelligentCache() if cache_enabled else None
        
        # 基础组件（必备策略）
        self.limiters = {provider: create_limiter("text") for provider in providers}
        self.monitors = {provider: PerformanceMonitor() for provider in providers}
    
    def process_request(self, content, priority=Priority.NORMAL, use_cache=True):
        """处理请求"""
        # 1. 检查缓存
        if self.cache and use_cache:
            cached_result = self.cache.get(content)
            if cached_result:
                return cached_result
        
        # 2. 选择提供商
        provider = self.load_balancer.select_provider() if self.load_balancer else self.providers[0]
        
        # 3. 调整并发数
        if self.concurrency_controller:
            max_concurrent = self.concurrency_controller.adjust_concurrency()
        
        # 4. 添加到优先级队列或直接处理
        if self.task_queue:
            task_id = self.task_queue.add_task(priority, self._execute_request, provider, content)
            # 这里需要实现任务执行器来处理队列中的任务
            result = self._wait_for_task_result(task_id)
        else:
            result = self._execute_request(provider, content)
        
        # 5. 缓存结果
        if self.cache and use_cache and result.get('success'):
            self.cache.set(content, result)
        
        return result
    
    def _execute_request(self, provider, content):
        """执行实际的API请求"""
        limiter = self.limiters[provider]
        monitor = self.monitors[provider]
        
        # 限流
        limiter.wait_for_token()
        
        # 监控
        start_time = monitor.record_request_start()
        
        try:
            # 这里调用实际的API
            response = self._call_api(provider, content)
            
            # 记录成功
            response_time = time.time() - start_time
            monitor.record_request_complete(start_time, success=True)
            
            if self.concurrency_controller:
                self.concurrency_controller.record_performance(response_time, True)
            
            if self.load_balancer:
                self.load_balancer.record_request_complete(provider, True, response_time)
            
            return {'success': True, 'response': response, 'provider': provider}
            
        except Exception as e:
            # 记录失败
            response_time = time.time() - start_time
            monitor.record_request_complete(start_time, success=False)
            
            if self.concurrency_controller:
                self.concurrency_controller.record_performance(response_time, False)
            
            if self.load_balancer:
                self.load_balancer.record_request_complete(provider, False, response_time)
            
            return {'success': False, 'error': str(e), 'provider': provider}
    
    def get_comprehensive_stats(self):
        """获取综合统计信息"""
        stats = {}
        
        if self.concurrency_controller:
            stats['concurrency'] = self.concurrency_controller.get_stats()
        
        if self.task_queue:
            stats['task_queue'] = self.task_queue.get_stats()
        
        if self.load_balancer:
            stats['load_balancer'] = self.load_balancer.get_stats()
        
        if self.cache:
            stats['cache'] = self.cache.get_stats()
        
        stats['monitors'] = {provider: monitor.get_metrics() 
                           for provider, monitor in self.monitors.items()}
        
        return stats

# 使用示例
scheduler = AdvancedAPIScheduler(
    providers=["siliconflow", "openrouter"],
    cache_enabled=True,
    load_balancing=True
)

# 处理高优先级请求
result = scheduler.process_request(
    "紧急任务：分析这个文本",
    priority=Priority.HIGH,
    use_cache=False
)

# 查看统计信息
stats = scheduler.get_comprehensive_stats()
print("综合统计:", json.dumps(stats, indent=2))
```

## 🎉 可选策略总结

这些可选策略可以根据具体需求选择性实现：

1. **自适应并发控制** - 根据性能动态调整并发数
2. **优先级队列** - 重要任务优先处理
3. **负载均衡** - 多API提供商之间分配负载
4. **智能缓存** - 减少重复请求，提高响应速度

这些策略可以单独使用，也可以组合使用，为API调度提供更高级的功能和更好的性能。
