# -*- coding: utf-8 -*-
"""
电商翻译系统 - 完整版本
======================

基于OpenRouter API的专业电商产品描述翻译系统，专门针对鞋类产品优化。
支持多语言高质量翻译，包含智能模型分配、质量检查、批量处理等功能。

主要功能：
- 多语言翻译（英语、德语、法语、西班牙语、意大利语）
- 智能模型分配（根据语言选择最优模型）
- 电商优化的翻译提示模板
- 翻译质量检查和评估
- 批量翻译处理
- 专业术语映射
- 结果自动保存

作者: AI Assistant
版本: 1.0.0
日期: 2025-08-01
"""

import requests
import json
import os
from typing import Optional, Dict, Any, List
from datetime import datetime

# =============================================================================
# 配置常量
# =============================================================================

# OpenRouter API配置
OPENROUTER_URL = "https://openrouter.ai/api/v1/chat/completions"
API_KEY = "YOUR_OPENROUTER_API_KEY_HERE"  # 请替换为您的实际API密钥
DEFAULT_MODEL = "google/gemini-2.5-pro"

# =============================================================================
# 配置类
# =============================================================================

class TranslationConfig:
    """
    翻译系统配置类

    包含语言映射、模型配置、翻译质量标准等配置信息。
    """

    # 支持的语言列表
    SUPPORTED_LANGUAGES = {
        'english': 'English',
        'german': 'German',
        'french': 'French',
        'spanish': 'Spanish',
        'italian': 'Italian'
    }

    # 语言特定模型映射
    # 根据不同语言的特点选择最适合的AI模型
    LANGUAGE_MODEL_MAPPING = {
        'german': "openai/chatgpt-4o-latest",      # ChatGPT在德语翻译上表现优异
        'spanish': "openai/chatgpt-4o-latest",     # ChatGPT在西班牙语翻译上表现优异
        'italian': "openai/chatgpt-4o-latest",     # ChatGPT在意大利语翻译上表现优异
        'french': "google/gemini-2.5-pro"          # Gemini在法语翻译上有特殊优势
    }

    # 默认翻译参数
    DEFAULT_TRANSLATION_PARAMS = {
        'temperature': 0.3,    # 较低温度确保翻译一致性
        'max_tokens': 4000,    # 足够长的产品描述
        'top_p': 0.9          # 控制输出的多样性
    }

class FootwearTranslationConfig:
    """
    鞋类产品翻译专用配置

    包含鞋类专业术语映射、尺码转换等特定配置。
    """

    # 鞋类专业术语映射
    # 确保专业术语的准确翻译
    FOOTWEAR_TERMINOLOGY = {
        'english_to_german': {
            'running shoes': 'Laufschuhe',
            'sneakers': 'Turnschuhe',
            'cushioning': 'Dämpfung',
            'breathable': 'atmungsaktiv',
            'outsole': 'Außensohle',
            'midsole': 'Zwischensohle',
            'upper': 'Obermaterial',
            'heel counter': 'Fersenkappe',
            'arch support': 'Fußgewölbestütze'
        },
        'english_to_french': {
            'running shoes': 'chaussures de course',
            'sneakers': 'baskets',
            'cushioning': 'amorti',
            'breathable': 'respirant',
            'outsole': 'semelle extérieure',
            'midsole': 'semelle intermédiaire',
            'upper': 'tige',
            'heel counter': 'contrefort',
            'arch support': 'support de voûte plantaire'
        },
        'english_to_spanish': {
            'running shoes': 'zapatillas para correr',
            'sneakers': 'zapatillas deportivas',
            'cushioning': 'amortiguación',
            'breathable': 'transpirable',
            'outsole': 'suela exterior',
            'midsole': 'entresuela',
            'upper': 'parte superior',
            'heel counter': 'contrafuerte del talón',
            'arch support': 'soporte del arco'
        },
        'english_to_italian': {
            'running shoes': 'scarpe da corsa',
            'sneakers': 'scarpe da ginnastica',
            'cushioning': 'ammortizzazione',
            'breathable': 'traspirante',
            'outsole': 'suola esterna',
            'midsole': 'intersuola',
            'upper': 'tomaia',
            'heel counter': 'contrafforte del tallone',
            'arch support': 'supporto dell\'arco plantare'
        }
    }

    # 尺码转换表
    # 帮助处理不同地区的尺码标准
    SIZE_CONVERSION = {
        'us_to_eu': {
            '6': '39', '6.5': '39.5', '7': '40', '7.5': '40.5',
            '8': '41', '8.5': '41.5', '9': '42', '9.5': '42.5',
            '10': '43', '10.5': '43.5', '11': '44', '11.5': '44.5',
            '12': '45'
        },
        'us_to_uk': {
            '6': '5.5', '6.5': '6', '7': '6.5', '7.5': '7',
            '8': '7.5', '8.5': '8', '9': '8.5', '9.5': '9',
            '10': '9.5', '10.5': '10', '11': '10.5', '11.5': '11',
            '12': '11.5'
        }
    }

class TranslationQualityChecker:
    """
    翻译质量检查器

    提供各种语言的翻译质量检查功能，确保翻译符合电商标准。
    """

    @staticmethod
    def check_german_quality(text: str) -> dict:
        """
        检查德语翻译质量

        Args:
            text (str): 待检查的德语文本

        Returns:
            dict: 包含质量评分、问题和建议的字典
        """
        issues = []
        suggestions = []

        # 检查是否使用了不当的医疗术语
        # 在电商产品描述中应避免医疗术语，使用更商业化的表达
        medical_terms = ['Linderung', 'Heilung', 'Therapie']
        for term in medical_terms:
            if term in text:
                issues.append(f"避免使用医疗术语: {term}")
                suggestions.append("使用 'Komfort' 或 'Bequemlichkeit' 替代")

        # 检查复合词大写
        # 德语复合词首字母应该大写
        if not any(word[0].isupper() for word in text.split() if len(word) > 8):
            suggestions.append("确保德语复合词首字母大写")

        return {
            'language': 'German',
            'issues': issues,
            'suggestions': suggestions,
            'score': max(0, 100 - len(issues) * 20)  # 每个问题扣20分
        }

    @staticmethod
    def check_french_quality(text: str) -> dict:
        """
        检查法语翻译质量

        Args:
            text (str): 待检查的法语文本

        Returns:
            dict: 包含质量评分、问题和建议的字典
        """
        issues = []
        suggestions = []

        # 检查性别一致性（简单检查）
        # 法语形容词需要与名词性别保持一致
        gender_patterns = ['beau ', 'belle ', 'nouveau ', 'nouvelle ']
        if not any(pattern in text.lower() for pattern in gender_patterns):
            suggestions.append("检查形容词性别一致性")

        # 检查缩写使用
        # 法语中应使用自然的缩写形式
        contractions = ['du ', 'au ', 'des ']
        if not any(contraction in text.lower() for contraction in contractions):
            suggestions.append("考虑使用自然的法语缩写")

        return {
            'language': 'French',
            'issues': issues,
            'suggestions': suggestions,
            'score': max(0, 100 - len(issues) * 20)
        }

    @staticmethod
    def check_translation_completeness(original: str, translation: str) -> dict:
        """
        检查翻译完整性

        通过比较原文和译文的长度来评估翻译的完整性。

        Args:
            original (str): 原文
            translation (str): 译文

        Returns:
            dict: 包含完整性评估结果的字典
        """
        original_length = len(original.split())
        translation_length = len(translation.split())

        # 计算长度比例（考虑语言差异）
        length_ratio = translation_length / original_length if original_length > 0 else 0

        issues = []
        # 翻译长度异常检查
        if length_ratio < 0.5:
            issues.append("翻译可能过于简短，可能遗漏了内容")
        elif length_ratio > 2.0:
            issues.append("翻译可能过于冗长，可能添加了不必要的内容")

        return {
            'original_words': original_length,
            'translation_words': translation_length,
            'length_ratio': length_ratio,
            'issues': issues,
            'completeness_score': max(0, 100 - len(issues) * 30)  # 每个问题扣30分
        }

# =============================================================================
# 示例产品描述
# =============================================================================

SAMPLE_PRODUCT_DESCRIPTIONS = {
    'running_shoes': """
    Premium Running Shoes with Advanced Cushioning Technology

    Experience ultimate comfort with our revolutionary running shoes featuring:
    - Ultra-lightweight breathable mesh upper
    - Advanced foam midsole for superior shock absorption
    - Durable rubber outsole with enhanced grip pattern
    - Ergonomic design for natural foot movement
    - Available in sizes 6-12 for men and women
    - Perfect for daily training, jogging, and casual wear

    Key Features:
    • Moisture-wicking interior lining
    • Reinforced heel counter for stability
    • Flexible forefoot design
    • Non-slip rubber sole
    • Machine washable materials

    Ideal for runners seeking performance, comfort, and style in one premium package.
    """,

    'casual_sneakers': """
    Stylish Casual Sneakers for Everyday Comfort

    Step out in style with these versatile sneakers that combine fashion and function:
    - Premium leather and canvas construction
    - Cushioned insole for all-day comfort
    - Non-slip rubber sole with classic tread pattern
    - Lace-up closure for secure fit
    - Available in multiple colors and sizes
    - Ideal for casual outings, work, and weekend activities
    """,

    'hiking_boots': """
    Waterproof Hiking Boots for Outdoor Adventures

    Conquer any terrain with these rugged hiking boots designed for serious outdoor enthusiasts:
    - Waterproof leather upper with reinforced toe cap
    - High-traction Vibram outsole for superior grip
    - Padded ankle collar for support and comfort
    - Moisture-wicking lining keeps feet dry
    - Steel shank for stability on uneven surfaces
    - Perfect for hiking, camping, and outdoor work
    """
}

# =============================================================================
# 主要翻译系统类
# =============================================================================

class EcommerceTranslationSystem:
    """
    电商翻译系统 - 专门用于鞋类产品翻译

    这是系统的核心类，提供完整的翻译功能，包括：
    - 单语言翻译
    - 批量多语言翻译
    - 智能模型分配
    - 翻译质量检查
    - 结果保存和管理

    Attributes:
        api_key (str): OpenRouter API密钥
        headers (dict): HTTP请求头
        config (TranslationConfig): 翻译配置实例
        quality_checker (TranslationQualityChecker): 质量检查器实例
    """

    # 语言特定模型配置
    # 根据语言特点选择最优模型以获得最佳翻译质量
    LANGUAGE_MODEL_MAPPING = {
        'german': "openai/chatgpt-4o-latest",      # ChatGPT在德语商业文本翻译上表现优异
        'spanish': "openai/chatgpt-4o-latest",     # ChatGPT在西班牙语商业文本翻译上表现优异
        'italian': "openai/chatgpt-4o-latest",     # ChatGPT在意大利语商业文本翻译上表现优异
        'french': "google/gemini-2.5-pro"          # Gemini在法语翻译上有特殊优势，特别是语言文化适应性
    }
    
    # 翻译提示模板
    TRANSLATION_PROMPT_TEMPLATE = """E-commerce Translation Task for Footwear Category:
Translate the following {origin_language} text into {target_language} for Amazon marketplace listings.

## Translation Requirements:
### Language Quality Standards:
- Provide natural, native-level {target_language} translation
- Use terminology appropriate for footwear/fashion industry
- Follow {target_language} e-commerce conventions and consumer expectations
- Ensure grammatical accuracy and proper spelling
- Match the tone and style suitable for product descriptions

### Content Preservation:
- Maintain all original meaning, technical specifications, and selling points
- Preserve product features, benefits, and key attributes
- Keep measurement units, sizes, and technical terms accurate
- Retain brand positioning and marketing appeal

### E-commerce Optimization:
- Use consumer-friendly language that drives purchasing decisions
- Employ terminology familiar to {target_language} online shoppers
- Ensure translation supports product searchability and conversion
- Avoid overly technical or medical terminology unless product-specific

### Universal Quality Standards:
- Avoid literal translations that sound unnatural to native speakers
- Use appropriate register for commercial/e-commerce context (avoid overly formal, literary, or technical language unless product-specific)
- Ensure grammatical accuracy including:
  * Gender/case agreement where applicable
  * Correct preposition usage
  * Proper spelling and spacing
  * Appropriate punctuation and capitalization
- Select terminology appropriate for the product category (avoid medical/religious terms for everyday products)
- Match cultural expectations and shopping behavior patterns of target market

### Language-Specific Refinements:
**German:** Standard commercial register; "Komfort" not "Linderung" for comfort features; proper compound noun capitalization
**French:** Gender agreement (beau/belle); natural contractions (du, au); e-commerce appropriate formality level  
**English:** American spelling for US market; natural phrasing over literal translation; industry-standard terminology

### Output Format:
Provide only the translated text without explanations, comments, or additional formatting.

Text to translate:
###
{text}
###"""

    def __init__(self, api_key: str = None):
        """
        初始化电商翻译系统

        Args:
            api_key (str, optional): OpenRouter API密钥。如果未提供，将使用全局API_KEY常量。
        """
        # 设置API密钥，优先使用传入的密钥，否则使用全局配置
        self.api_key = api_key or API_KEY

        # 设置HTTP请求头
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        # 初始化配置和质量检查器实例
        self.config = TranslationConfig()
        self.footwear_config = FootwearTranslationConfig()
        self.quality_checker = TranslationQualityChecker()

        # 打印初始化信息
        print("🌍 电商翻译系统已初始化")
        print(f"📋 支持语言: {', '.join(self.config.SUPPORTED_LANGUAGES.values())}")
        print(f"🤖 默认模型: {DEFAULT_MODEL}")
    
    def get_model_for_language(self, target_language: str) -> str:
        """
        根据目标语言获取对应的最优模型

        不同语言使用不同的AI模型以获得最佳翻译质量：
        - 德语、西班牙语、意大利语：使用ChatGPT-4o-latest
        - 法语：使用Gemini-2.5-pro（在法语翻译上有特殊优势）

        Args:
            target_language (str): 目标语言名称（如 "German", "French"）

        Returns:
            str: 对应的模型名称
        """
        language_key = target_language.lower()
        model = self.LANGUAGE_MODEL_MAPPING.get(language_key, DEFAULT_MODEL)
        return model

    def create_translation_prompt(self, text: str, origin_language: str = "English",
                                target_language: str = "German") -> str:
        """
        创建翻译提示

        使用专门优化的提示模板，包含电商翻译的所有要求和标准。

        Args:
            text (str): 待翻译的文本
            origin_language (str): 源语言，默认为"English"
            target_language (str): 目标语言，默认为"German"

        Returns:
            str: 格式化后的翻译提示
        """
        return self.TRANSLATION_PROMPT_TEMPLATE.format(
            origin_language=origin_language,
            target_language=target_language,
            text=text
        )
    
    def translate_text(self, text: str, target_language: str = "German",
                      origin_language: str = "English", **kwargs) -> Optional[Dict[str, Any]]:
        """
        翻译文本的核心方法

        执行单个文本的翻译，自动选择最优模型并应用专业的翻译提示。

        Args:
            text (str): 待翻译的文本内容
            target_language (str): 目标语言，默认为"German"
            origin_language (str): 源语言，默认为"English"
            **kwargs: 额外的翻译参数
                - temperature (float): 控制输出随机性，默认0.3
                - max_tokens (int): 最大输出token数
                - top_p (float): 核采样参数

        Returns:
            Optional[Dict[str, Any]]: API响应结果，包含翻译内容；失败时返回None
        """
        try:
            # 获取对应语言的模型
            model = self.get_model_for_language(target_language)
            
            # 创建翻译提示
            prompt = self.create_translation_prompt(text, origin_language, target_language)
            
            # 构建请求负载
            payload = {
                "model": model,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": kwargs.get("temperature", 0.3),  # 翻译任务使用较低温度
                **{k: v for k, v in kwargs.items() if k != "temperature"}
            }
            
            # 发送请求
            response = requests.post(OPENROUTER_URL, headers=self.headers, json=payload)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 翻译成功: {origin_language} -> {target_language} (模型: {model})")
                return result
            else:
                print(f"❌ 翻译请求失败，状态码：{response.status_code}")
                print(f"错误信息：{response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 翻译错误: {str(e)}")
            return None
    
    def batch_translate(self, text: str, target_languages: List[str] = None, 
                       origin_language: str = "English") -> Dict[str, Any]:
        """批量翻译到多种语言"""
        if target_languages is None:
            target_languages = ["German", "French", "Italian", "Spanish"]
        
        results = {}
        
        for target_lang in target_languages:
            print(f"\n翻译到 {target_lang}...")
            result = self.translate_text(text, target_lang, origin_language)
            results[target_lang] = result
        
        return results
    
    def extract_translation_content(self, api_response: Dict[str, Any]) -> Optional[str]:
        """提取翻译结果内容"""
        try:
            if "choices" in api_response and len(api_response["choices"]) > 0:
                choice = api_response["choices"][0]
                if "message" in choice and "content" in choice["message"]:
                    return choice["message"]["content"].strip()
            return None
        except Exception as e:
            print(f"❌ 提取翻译内容失败: {e}")
            return None
    
    def save_translation_results(self, results: Dict[str, Any], filename: str = None) -> bool:
        """保存翻译结果到JSON文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"translation_results_{timestamp}.json"
        
        try:
            # 处理结果，提取翻译内容
            processed_results = {}
            for language, result in results.items():
                if result:
                    translation = self.extract_translation_content(result)
                    processed_results[language] = {
                        'translation': translation,
                        'model_used': self.get_model_for_language(language),
                        'timestamp': datetime.now().isoformat()
                    }
                else:
                    processed_results[language] = {
                        'translation': None,
                        'error': 'Translation failed',
                        'timestamp': datetime.now().isoformat()
                    }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(processed_results, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 翻译结果已保存到: {filename}")
            return True
            
        except Exception as e:
            print(f"❌ 保存翻译结果失败: {e}")
            return False

# --- 配置验证 ---
def validate_config() -> bool:
    """验证配置"""
    if API_KEY == "YOUR_OPENROUTER_API_KEY_HERE":
        print("⚠️ 请先设置OpenRouter API密钥")
        return False
    return True

# --- 使用示例 ---
def example_usage():
    """基本使用示例"""
    if not validate_config():
        print("请在文件中设置正确的API密钥")
        return
    
    # 初始化翻译系统
    translator = EcommerceTranslationSystem(api_key=API_KEY)
    
    # 示例产品描述
    sample_text = """
    Premium Running Shoes with Advanced Cushioning Technology
    
    Experience ultimate comfort with our revolutionary running shoes featuring:
    - Ultra-lightweight breathable mesh upper
    - Advanced foam midsole for superior shock absorption  
    - Durable rubber outsole with enhanced grip pattern
    - Ergonomic design for natural foot movement
    - Available in sizes 6-12 for men and women
    - Perfect for daily training, jogging, and casual wear
    """
    
    print("=== 电商翻译系统示例 ===\n")
    print("原文 (English):")
    print(sample_text)
    print("\n" + "-"*60 + "\n")
    
    # 单语言翻译示例
    print("1. 单语言翻译 (英语 -> 德语):")
    german_result = translator.translate_text(
        text=sample_text,
        target_language="German",
        origin_language="English"
    )
    
    if german_result:
        german_translation = translator.extract_translation_content(german_result)
        if german_translation:
            print(f"德语翻译:\n{german_translation}\n")
    
    # 批量翻译示例
    print("2. 批量翻译到多种语言:")
    batch_results = translator.batch_translate(
        text=sample_text,
        target_languages=["German", "French", "Spanish", "Italian"],
        origin_language="English"
    )
    
    # 保存结果
    translator.save_translation_results(batch_results)
    
    print("\n=== 批量翻译结果 ===")
    for language, result in batch_results.items():
        if result:
            translation = translator.extract_translation_content(result)
            if translation:
                print(f"\n{language}:")
                print(f"{translation[:200]}..." if len(translation) > 200 else translation)
                print("-" * 50)

if __name__ == "__main__":
    example_usage()
