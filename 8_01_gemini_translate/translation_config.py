# -*- coding: utf-8 -*-
"""
电商翻译系统配置文件
包含语言映射、模型配置、翻译质量标准等
"""

# --- 语言和模型配置 ---
class TranslationConfig:
    """翻译系统配置类"""
    
    # 支持的语言列表
    SUPPORTED_LANGUAGES = {
        'english': 'English',
        'german': 'German', 
        'french': 'French',
        'spanish': 'Spanish',
        'italian': 'Italian'
    }
    
    # 语言特定模型映射
    LANGUAGE_MODEL_MAPPING = {
        'german': "openai/chatgpt-4o-latest",
        'spanish': "openai/chatgpt-4o-latest", 
        'italian': "openai/chatgpt-4o-latest",
        'french': "google/gemini-2.5-pro"
    }
    
    # 默认翻译参数
    DEFAULT_TRANSLATION_PARAMS = {
        'temperature': 0.3,  # 较低温度确保翻译一致性
        'max_tokens': 4000,  # 足够长的产品描述
        'top_p': 0.9
    }
    
    # 语言特定的翻译质量检查关键词
    QUALITY_CHECK_KEYWORDS = {
        'german': {
            'comfort_terms': ['Komfort', 'bequem', 'angenehm'],
            'avoid_terms': ['Linderung', 'Heilung'],  # 避免医疗术语
            'required_patterns': ['compound_nouns']  # 德语复合词
        },
        'french': {
            'gender_agreement': ['beau/belle', 'nouveau/nouvelle'],
            'contractions': ['du', 'au', 'des'],
            'formality': 'commercial'  # 商业正式程度
        },
        'spanish': {
            'regional_preference': 'neutral',  # 中性西班牙语
            'formality': 'commercial'
        },
        'italian': {
            'regional_preference': 'standard',
            'formality': 'commercial'
        }
    }

# --- 产品类别特定配置 ---
class FootwearTranslationConfig:
    """鞋类产品翻译专用配置"""
    
    # 鞋类专业术语映射
    FOOTWEAR_TERMINOLOGY = {
        'english_to_german': {
            'running shoes': 'Laufschuhe',
            'sneakers': 'Turnschuhe',
            'cushioning': 'Dämpfung',
            'breathable': 'atmungsaktiv',
            'outsole': 'Außensohle',
            'midsole': 'Zwischensohle',
            'upper': 'Obermaterial',
            'heel counter': 'Fersenkappe',
            'arch support': 'Fußgewölbestütze'
        },
        'english_to_french': {
            'running shoes': 'chaussures de course',
            'sneakers': 'baskets',
            'cushioning': 'amorti',
            'breathable': 'respirant',
            'outsole': 'semelle extérieure',
            'midsole': 'semelle intermédiaire',
            'upper': 'tige',
            'heel counter': 'contrefort',
            'arch support': 'support de voûte plantaire'
        },
        'english_to_spanish': {
            'running shoes': 'zapatillas para correr',
            'sneakers': 'zapatillas deportivas',
            'cushioning': 'amortiguación',
            'breathable': 'transpirable',
            'outsole': 'suela exterior',
            'midsole': 'entresuela',
            'upper': 'parte superior',
            'heel counter': 'contrafuerte del talón',
            'arch support': 'soporte del arco'
        },
        'english_to_italian': {
            'running shoes': 'scarpe da corsa',
            'sneakers': 'scarpe da ginnastica',
            'cushioning': 'ammortizzazione',
            'breathable': 'traspirante',
            'outsole': 'suola esterna',
            'midsole': 'intersuola',
            'upper': 'tomaia',
            'heel counter': 'contrafforte del tallone',
            'arch support': 'supporto dell\'arco plantare'
        }
    }
    
    # 尺码转换表
    SIZE_CONVERSION = {
        'us_to_eu': {
            '6': '39', '6.5': '39.5', '7': '40', '7.5': '40.5',
            '8': '41', '8.5': '41.5', '9': '42', '9.5': '42.5',
            '10': '43', '10.5': '43.5', '11': '44', '11.5': '44.5',
            '12': '45'
        },
        'us_to_uk': {
            '6': '5.5', '6.5': '6', '7': '6.5', '7.5': '7',
            '8': '7.5', '8.5': '8', '9': '8.5', '9.5': '9',
            '10': '9.5', '10.5': '10', '11': '10.5', '11.5': '11',
            '12': '11.5'
        }
    }

# --- 翻译质量评估 ---
class TranslationQualityChecker:
    """翻译质量检查器"""
    
    @staticmethod
    def check_german_quality(text: str) -> dict:
        """检查德语翻译质量"""
        issues = []
        suggestions = []
        
        # 检查是否使用了不当的医疗术语
        medical_terms = ['Linderung', 'Heilung', 'Therapie']
        for term in medical_terms:
            if term in text:
                issues.append(f"避免使用医疗术语: {term}")
                suggestions.append("使用 'Komfort' 或 'Bequemlichkeit' 替代")
        
        # 检查复合词大写
        if not any(word[0].isupper() for word in text.split() if len(word) > 8):
            suggestions.append("确保德语复合词首字母大写")
        
        return {
            'language': 'German',
            'issues': issues,
            'suggestions': suggestions,
            'score': max(0, 100 - len(issues) * 20)
        }
    
    @staticmethod
    def check_french_quality(text: str) -> dict:
        """检查法语翻译质量"""
        issues = []
        suggestions = []
        
        # 检查性别一致性（简单检查）
        gender_patterns = ['beau ', 'belle ', 'nouveau ', 'nouvelle ']
        if not any(pattern in text.lower() for pattern in gender_patterns):
            suggestions.append("检查形容词性别一致性")
        
        # 检查缩写使用
        contractions = ['du ', 'au ', 'des ']
        if not any(contraction in text.lower() for contraction in contractions):
            suggestions.append("考虑使用自然的法语缩写")
        
        return {
            'language': 'French',
            'issues': issues,
            'suggestions': suggestions,
            'score': max(0, 100 - len(issues) * 20)
        }
    
    @staticmethod
    def check_translation_completeness(original: str, translation: str) -> dict:
        """检查翻译完整性"""
        original_length = len(original.split())
        translation_length = len(translation.split())
        
        # 计算长度比例（考虑语言差异）
        length_ratio = translation_length / original_length if original_length > 0 else 0
        
        issues = []
        if length_ratio < 0.5:
            issues.append("翻译可能过于简短，可能遗漏了内容")
        elif length_ratio > 2.0:
            issues.append("翻译可能过于冗长，可能添加了不必要的内容")
        
        return {
            'original_words': original_length,
            'translation_words': translation_length,
            'length_ratio': length_ratio,
            'issues': issues,
            'completeness_score': max(0, 100 - len(issues) * 30)
        }

# --- 示例产品描述 ---
SAMPLE_PRODUCT_DESCRIPTIONS = {
    'running_shoes': """
    Premium Running Shoes with Advanced Cushioning Technology
    
    Experience ultimate comfort with our revolutionary running shoes featuring:
    - Ultra-lightweight breathable mesh upper
    - Advanced foam midsole for superior shock absorption  
    - Durable rubber outsole with enhanced grip pattern
    - Ergonomic design for natural foot movement
    - Available in sizes 6-12 for men and women
    - Perfect for daily training, jogging, and casual wear
    
    Key Features:
    • Moisture-wicking interior lining
    • Reinforced heel counter for stability
    • Flexible forefoot design
    • Non-slip rubber sole
    • Machine washable materials
    
    Ideal for runners seeking performance, comfort, and style in one premium package.
    """,
    
    'casual_sneakers': """
    Stylish Casual Sneakers for Everyday Comfort
    
    Step out in style with these versatile sneakers that combine fashion and function:
    - Premium leather and canvas construction
    - Cushioned insole for all-day comfort
    - Non-slip rubber sole with classic tread pattern
    - Lace-up closure for secure fit
    - Available in multiple colors and sizes
    - Ideal for casual outings, work, and weekend activities
    """,
    
    'hiking_boots': """
    Waterproof Hiking Boots for Outdoor Adventures
    
    Conquer any terrain with these rugged hiking boots designed for serious outdoor enthusiasts:
    - Waterproof leather upper with reinforced toe cap
    - High-traction Vibram outsole for superior grip
    - Padded ankle collar for support and comfort
    - Moisture-wicking lining keeps feet dry
    - Steel shank for stability on uneven surfaces
    - Perfect for hiking, camping, and outdoor work
    """
}

# --- 批量翻译配置 ---
class BatchTranslationConfig:
    """批量翻译配置"""
    
    # 默认目标语言列表
    DEFAULT_TARGET_LANGUAGES = ['German', 'French', 'Spanish', 'Italian']
    
    # 批量处理参数
    BATCH_PARAMS = {
        'max_concurrent_requests': 3,  # 最大并发请求数
        'delay_between_requests': 1.0,  # 请求间延迟（秒）
        'retry_attempts': 2,  # 重试次数
        'timeout': 30  # 请求超时时间
    }
    
    # 输出格式配置
    OUTPUT_FORMATS = {
        'json': {
            'extension': '.json',
            'structure': 'nested'
        },
        'csv': {
            'extension': '.csv',
            'columns': ['original_text', 'language', 'translation', 'model_used']
        },
        'txt': {
            'extension': '.txt',
            'separator': '\n---\n'
        }
    }

# --- 导出配置 ---
__all__ = [
    'TranslationConfig',
    'FootwearTranslationConfig', 
    'TranslationQualityChecker',
    'BatchTranslationConfig',
    'SAMPLE_PRODUCT_DESCRIPTIONS'
]
