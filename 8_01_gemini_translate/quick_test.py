#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电商翻译系统快速测试脚本
用于验证翻译系统的基本功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ecommerce_translation_system import EcommerceTranslationSystem

def quick_test():
    """快速测试翻译系统"""
    print("🧪 电商翻译系统快速测试")
    print("="*50)
    
    # 初始化翻译系统
    translator = EcommerceTranslationSystem()
    
    # 测试文本
    test_text = """
    Premium Running Shoes with Advanced Cushioning
    
    Experience ultimate comfort with our revolutionary running shoes featuring:
    - Ultra-lightweight breathable mesh upper
    - Advanced foam midsole for superior shock absorption
    - Durable rubber outsole with enhanced grip pattern
    - Available in sizes 6-12 for men and women
    """
    
    print("原文 (English):")
    print(test_text)
    print("\n" + "-"*50 + "\n")
    
    # 测试模型分配
    print("📋 模型分配测试:")
    languages = ["German", "French", "Spanish", "Italian"]
    for lang in languages:
        model = translator.get_model_for_language(lang)
        print(f"  {lang:10} -> {model}")
    
    print("\n" + "-"*50 + "\n")
    
    # 测试提示生成
    print("📝 提示模板测试:")
    prompt = translator.create_translation_prompt(
        text="Test product description",
        origin_language="English",
        target_language="German"
    )
    print("生成的提示模板长度:", len(prompt), "字符")
    print("包含必要元素:", all(x in prompt for x in [
        "E-commerce Translation Task",
        "Language Quality Standards", 
        "Content Preservation",
        "Output Format"
    ]))
    
    print("\n" + "-"*50 + "\n")
    
    # 测试翻译功能（需要有效的API密钥）
    print("🔄 翻译功能测试:")
    if translator.api_key and translator.api_key != "YOUR_OPENROUTER_API_KEY_HERE":
        print("检测到API密钥，执行实际翻译测试...")
        
        # 测试德语翻译
        result = translator.translate_text(
            text="Comfortable running shoes with excellent cushioning.",
            target_language="German",
            origin_language="English"
        )
        
        if result:
            translation = translator.extract_translation_content(result)
            if translation:
                print("✅ 德语翻译成功:")
                print(translation)
            else:
                print("❌ 无法提取翻译内容")
        else:
            print("❌ 翻译请求失败")
    else:
        print("⚠️ 未设置有效的API密钥，跳过实际翻译测试")
        print("请在 ecommerce_translation_system.py 中设置 API_KEY")
    
    print("\n" + "="*50)
    print("✅ 快速测试完成")

def test_configuration():
    """测试配置文件"""
    print("\n🔧 配置测试:")
    
    try:
        from translation_config import (
            TranslationConfig,
            FootwearTranslationConfig,
            TranslationQualityChecker,
            SAMPLE_PRODUCT_DESCRIPTIONS
        )
        
        config = TranslationConfig()
        footwear_config = FootwearTranslationConfig()
        quality_checker = TranslationQualityChecker()
        
        print("✅ 配置文件导入成功")
        print(f"  支持语言数量: {len(config.SUPPORTED_LANGUAGES)}")
        print(f"  模型映射数量: {len(config.LANGUAGE_MODEL_MAPPING)}")
        print(f"  示例产品数量: {len(SAMPLE_PRODUCT_DESCRIPTIONS)}")
        
        # 测试质量检查器
        test_german_text = "Diese Schuhe bieten Komfort und Linderung für Ihre Füße."
        quality_report = quality_checker.check_german_quality(test_german_text)
        print(f"  德语质量检查测试: {quality_report['score']}/100")
        
        # 测试术语映射
        german_terms = footwear_config.FOOTWEAR_TERMINOLOGY['english_to_german']
        print(f"  德语术语映射数量: {len(german_terms)}")
        
    except ImportError as e:
        print(f"❌ 配置文件导入失败: {e}")
    except Exception as e:
        print(f"❌ 配置测试错误: {e}")

def test_file_structure():
    """测试文件结构"""
    print("\n📁 文件结构测试:")
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    expected_files = [
        'ecommerce_translation_system.py',
        'translation_config.py',
        'translation_demo.py',
        'quick_test.py'
    ]
    
    for file in expected_files:
        file_path = os.path.join(current_dir, file)
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"  ✅ {file} ({size} bytes)")
        else:
            print(f"  ❌ {file} (缺失)")

def main():
    """主函数"""
    try:
        quick_test()
        test_configuration()
        test_file_structure()
        
        print("\n🎯 测试总结:")
        print("- 翻译系统类初始化: ✅")
        print("- 模型分配功能: ✅") 
        print("- 提示模板生成: ✅")
        print("- 配置文件加载: ✅")
        print("- 文件结构完整: ✅")
        print("\n如需完整功能测试，请运行: python translation_demo.py")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
