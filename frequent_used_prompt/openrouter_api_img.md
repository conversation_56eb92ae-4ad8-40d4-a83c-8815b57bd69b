以下是使用openrouter调度api处理图片的对应的代码：

code:
```
import requests
import json
import base64

def encode_image_to_base64(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

# API配置
url = "https://openrouter.ai/api/v1/chat/completions"
api_key = "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"

headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json"
}

# 读取并编码图片
image_path = "/data2/jevon/vl_test/pictures/20250627-173252.jpeg"
base64_image = encode_image_to_base64(image_path)
data_url = f"data:image/jpeg;base64,{base64_image}"

# 构建消息
messages = [
    {
        "role": "user",
        "content": [
            {
                "type": "text",
                "text": "Give the image prompt."
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": data_url
                }
            }
        ]
    }
]

# 构建请求负载
payload = {
    "model": "google/gemini-2.5-pro",
    "messages": messages
}

# 发送请求
try:
    response = requests.post(url, headers=headers, json=payload)
    
    if response.status_code == 200:
        result = response.json()
        print("请求成功！")
        print("响应内容：")
        print(json.dumps(result, indent=2, ensure_ascii=False))
    else:
        print(f"请求失败，状态码：{response.status_code}")
        print("错误信息：", response.text)
        
except FileNotFoundError:
    print(f"错误：找不到图片文件 {image_path}")
except Exception as e:
    print(f"发生错误：{str(e)}")
```