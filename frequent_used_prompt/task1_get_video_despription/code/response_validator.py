#!/usr/bin/env python3
"""
简化响应格式验证器
移除复杂的智能修复机制，实现快速失败的JSON解析策略
"""

import json
import threading
from typing import Dict, Optional


class SimpleResponseValidator:
    """简化响应格式验证器 - 快速失败策略"""

    def __init__(self, max_retries: int = 3):
        """
        初始化验证器

        Args:
            max_retries (int): 最大重试次数，超过则直接失败
        """
        self.max_retries = max_retries
        self.lock = threading.Lock()

        # 统计信息
        self.stats = {
            'total_attempts': 0,
            'successful_parses': 0,
            'failed_parses': 0,
            'retry_counts': {}
        }

        print(f"🔧 初始化简化JSON验证器，最大重试次数: {max_retries}")
    
    def validate_and_fix_response(self, response_text: str, video_path: str = "unknown") -> Optional[Dict]:
        """
        简化的响应验证 - 快速失败策略

        Args:
            response_text (str): 原始API响应文本
            video_path (str): 视频路径（用于统计）

        Returns:
            Optional[Dict]: 解析后的JSON数据，失败返回None
        """
        with self.lock:
            self.stats['total_attempts'] += 1

        if not response_text or not response_text.strip():
            print("   ❌ 响应内容为空")
            self._record_failure(video_path)
            return None

        # 只尝试标准JSON解析，不进行复杂修复
        parsed_data = self._simple_json_parse(response_text)

        if parsed_data is not None:
            print("   ✅ JSON解析成功")
            self._record_success(video_path)
            return parsed_data
        else:
            print("   ❌ JSON解析失败")
            self._record_failure(video_path)
            return None
    
    def _simple_json_parse(self, response_text: str) -> Optional[Dict]:
        """
        简化的JSON解析 - 只尝试基本清理和标准解析

        Args:
            response_text (str): 响应文本

        Returns:
            Optional[Dict]: 解析结果，失败返回None
        """
        cleaned_response = response_text.strip()

        # 基本清理：移除代码块标记
        if cleaned_response.startswith("```json"):
            cleaned_response = cleaned_response[len("```json"):].strip()
        elif cleaned_response.startswith("```"):
            cleaned_response = cleaned_response[len("```"):].strip()

        if cleaned_response.endswith("```"):
            cleaned_response = cleaned_response[:-len("```")].strip()

        # 尝试标准JSON解析
        try:
            result = json.loads(cleaned_response)
            if isinstance(result, dict):
                return result
        except json.JSONDecodeError:
            pass

        # 如果标准解析失败，尝试查找JSON对象
        try:
            # 查找第一个完整的JSON对象
            start_idx = cleaned_response.find('{')
            if start_idx != -1:
                brace_count = 0
                for i, char in enumerate(cleaned_response[start_idx:], start_idx):
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            json_str = cleaned_response[start_idx:i+1]
                            result = json.loads(json_str)
                            if isinstance(result, dict):
                                return result
                            break
        except (json.JSONDecodeError, ValueError):
            pass

        return None

    def _record_success(self, video_path: str):
        """记录解析成功"""
        self.stats['successful_parses'] += 1
        if video_path in self.stats['retry_counts']:
            del self.stats['retry_counts'][video_path]

    def _record_failure(self, video_path: str):
        """记录解析失败"""
        self.stats['failed_parses'] += 1
        if video_path not in self.stats['retry_counts']:
            self.stats['retry_counts'][video_path] = 0
        self.stats['retry_counts'][video_path] += 1

    def should_retry(self, video_path: str) -> bool:
        """
        判断是否应该重试解析

        Args:
            video_path (str): 视频路径

        Returns:
            bool: 是否应该重试
        """
        retry_count = self.stats['retry_counts'].get(video_path, 0)
        return retry_count < self.max_retries

    def get_retry_count(self, video_path: str) -> int:
        """
        获取视频的重试次数

        Args:
            video_path (str): 视频路径

        Returns:
            int: 重试次数
        """
        return self.stats['retry_counts'].get(video_path, 0)

    def get_statistics(self) -> Dict:
        """获取解析统计信息"""
        with self.lock:
            total_attempts = self.stats['total_attempts']
            success_rate = (self.stats['successful_parses'] / total_attempts * 100) if total_attempts > 0 else 0

            return {
                'total_attempts': total_attempts,
                'successful_parses': self.stats['successful_parses'],
                'failed_parses': self.stats['failed_parses'],
                'success_rate': success_rate,
                'videos_with_retries': len(self.stats['retry_counts']),
                'max_retries': self.max_retries
            }

    def print_statistics(self):
        """打印解析统计信息"""
        stats = self.get_statistics()
        print(f"\n{'='*60}")
        print("JSON解析统计信息:")
        print(f"{'='*60}")
        print(f"总尝试次数: {stats['total_attempts']}")
        print(f"成功解析: {stats['successful_parses']}")
        print(f"失败解析: {stats['failed_parses']}")
        print(f"成功率: {stats['success_rate']:.1f}%")
        print(f"需要重试的视频: {stats['videos_with_retries']}")
        print(f"最大重试次数: {stats['max_retries']}")

    def reset_statistics(self):
        """重置统计信息"""
        with self.lock:
            self.stats = {
                'total_attempts': 0,
                'successful_parses': 0,
                'failed_parses': 0,
                'retry_counts': {}
            }
            print("🔄 JSON解析统计信息已重置")

