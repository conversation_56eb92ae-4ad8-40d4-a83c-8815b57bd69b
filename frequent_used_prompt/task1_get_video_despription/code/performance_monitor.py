#!/usr/bin/env python3
"""
性能监控模块
实时监控系统性能、资源使用情况和处理统计
"""

import time
import threading
import psutil
import os
from typing import Dict, List, Optional
from collections import deque
from dataclasses import dataclass


@dataclass
class PerformanceSnapshot:
    """性能快照数据类"""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    active_threads: int
    api_calls_per_minute: float
    success_rate: float
    processing_speed: float  # videos per minute


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, history_size: int = 100, monitor_interval: float = 10.0):
        """
        初始化性能监控器
        
        Args:
            history_size (int): 保留的历史数据点数量
            monitor_interval (float): 监控间隔（秒）
        """
        self.history_size = history_size
        self.monitor_interval = monitor_interval
        
        # 性能历史数据
        self.performance_history = deque(maxlen=history_size)
        
        # 监控线程
        self.monitor_thread = None
        self.monitoring = False
        self.lock = threading.Lock()
        
        # 统计数据
        self.stats = {
            'start_time': time.time(),
            'total_videos_processed': 0,
            'total_api_calls': 0,
            'successful_api_calls': 0,
            'last_update_time': time.time(),
            'last_video_count': 0,
            'last_api_count': 0
        }
        
        # 进程信息
        self.process = psutil.Process(os.getpid())
        
        print(f"📊 初始化性能监控器，监控间隔: {monitor_interval}秒")
    
    def start_monitoring(self):
        """启动性能监控"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        print("📊 性能监控已启动")
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.monitoring = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        print("📊 性能监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                snapshot = self._take_snapshot()
                with self.lock:
                    self.performance_history.append(snapshot)
                
                # 检查性能异常
                self._check_performance_alerts(snapshot)
                
                time.sleep(self.monitor_interval)
                
            except Exception as e:
                print(f"⚠️ 性能监控异常: {e}")
                time.sleep(self.monitor_interval)
    
    def _take_snapshot(self) -> PerformanceSnapshot:
        """获取当前性能快照"""
        current_time = time.time()
        
        # 系统资源使用情况
        cpu_percent = self.process.cpu_percent()
        memory_info = self.process.memory_info()
        memory_percent = self.process.memory_percent()
        memory_used_mb = memory_info.rss / (1024 * 1024)
        
        # 线程数量
        active_threads = threading.active_count()
        
        # 计算处理速度
        with self.lock:
            time_elapsed = current_time - self.stats['last_update_time']
            
            if time_elapsed > 0:
                # API调用速度（每分钟）
                api_calls_delta = self.stats['total_api_calls'] - self.stats['last_api_count']
                api_calls_per_minute = (api_calls_delta / time_elapsed) * 60
                
                # 视频处理速度（每分钟）
                videos_delta = self.stats['total_videos_processed'] - self.stats['last_video_count']
                processing_speed = (videos_delta / time_elapsed) * 60
                
                # 更新统计
                self.stats['last_update_time'] = current_time
                self.stats['last_video_count'] = self.stats['total_videos_processed']
                self.stats['last_api_count'] = self.stats['total_api_calls']
            else:
                api_calls_per_minute = 0
                processing_speed = 0
            
            # 成功率
            if self.stats['total_api_calls'] > 0:
                success_rate = (self.stats['successful_api_calls'] / self.stats['total_api_calls']) * 100
            else:
                success_rate = 0
        
        return PerformanceSnapshot(
            timestamp=current_time,
            cpu_percent=cpu_percent,
            memory_percent=memory_percent,
            memory_used_mb=memory_used_mb,
            active_threads=active_threads,
            api_calls_per_minute=api_calls_per_minute,
            success_rate=success_rate,
            processing_speed=processing_speed
        )
    
    def _check_performance_alerts(self, snapshot: PerformanceSnapshot):
        """检查性能警报"""
        alerts = []
        
        # CPU使用率过高
        if snapshot.cpu_percent > 90:
            alerts.append(f"CPU使用率过高: {snapshot.cpu_percent:.1f}%")
        
        # 内存使用率过高
        if snapshot.memory_percent > 85:
            alerts.append(f"内存使用率过高: {snapshot.memory_percent:.1f}%")
        
        # API成功率过低
        if snapshot.success_rate < 70 and self.stats['total_api_calls'] > 10:
            alerts.append(f"API成功率过低: {snapshot.success_rate:.1f}%")
        
        # 处理速度过慢
        if snapshot.processing_speed < 1 and self.stats['total_videos_processed'] > 5:
            alerts.append(f"处理速度过慢: {snapshot.processing_speed:.2f} 视频/分钟")
        
        # 打印警报
        for alert in alerts:
            print(f"⚠️ 性能警报: {alert}")
    
    def update_stats(self, videos_processed: int = 0, api_calls: int = 0, successful_api_calls: int = 0):
        """
        更新统计数据
        
        Args:
            videos_processed (int): 新处理的视频数量
            api_calls (int): 新的API调用数量
            successful_api_calls (int): 新的成功API调用数量
        """
        with self.lock:
            self.stats['total_videos_processed'] += videos_processed
            self.stats['total_api_calls'] += api_calls
            self.stats['successful_api_calls'] += successful_api_calls
    
    def get_current_stats(self) -> Dict:
        """获取当前统计信息"""
        with self.lock:
            current_time = time.time()
            runtime = current_time - self.stats['start_time']
            
            # 获取最新快照
            latest_snapshot = None
            if self.performance_history:
                latest_snapshot = self.performance_history[-1]
            
            return {
                'runtime_seconds': runtime,
                'total_videos_processed': self.stats['total_videos_processed'],
                'total_api_calls': self.stats['total_api_calls'],
                'successful_api_calls': self.stats['successful_api_calls'],
                'overall_success_rate': (self.stats['successful_api_calls'] / max(1, self.stats['total_api_calls'])) * 100,
                'avg_processing_speed': (self.stats['total_videos_processed'] / max(1, runtime / 60)),  # videos per minute
                'current_snapshot': latest_snapshot
            }
    
    def get_performance_summary(self) -> Dict:
        """获取性能摘要"""
        if not self.performance_history:
            return {'error': '暂无性能数据'}
        
        with self.lock:
            snapshots = list(self.performance_history)
        
        # 计算平均值和趋势
        cpu_values = [s.cpu_percent for s in snapshots]
        memory_values = [s.memory_percent for s in snapshots]
        speed_values = [s.processing_speed for s in snapshots if s.processing_speed > 0]
        
        return {
            'data_points': len(snapshots),
            'time_range_minutes': (snapshots[-1].timestamp - snapshots[0].timestamp) / 60,
            'cpu_usage': {
                'avg': sum(cpu_values) / len(cpu_values),
                'max': max(cpu_values),
                'current': snapshots[-1].cpu_percent
            },
            'memory_usage': {
                'avg': sum(memory_values) / len(memory_values),
                'max': max(memory_values),
                'current': snapshots[-1].memory_percent,
                'current_mb': snapshots[-1].memory_used_mb
            },
            'processing_speed': {
                'avg': sum(speed_values) / len(speed_values) if speed_values else 0,
                'max': max(speed_values) if speed_values else 0,
                'current': snapshots[-1].processing_speed
            },
            'threads': {
                'current': snapshots[-1].active_threads,
                'avg': sum(s.active_threads for s in snapshots) / len(snapshots)
            }
        }
    
    def print_performance_report(self):
        """打印性能报告"""
        stats = self.get_current_stats()
        summary = self.get_performance_summary()
        
        print(f"\n{'='*60}")
        print("📊 性能监控报告")
        print(f"{'='*60}")
        
        # 基本统计
        print(f"运行时间: {stats['runtime_seconds']:.1f} 秒")
        print(f"已处理视频: {stats['total_videos_processed']} 个")
        print(f"API调用总数: {stats['total_api_calls']} 次")
        print(f"API成功率: {stats['overall_success_rate']:.1f}%")
        print(f"平均处理速度: {stats['avg_processing_speed']:.2f} 视频/分钟")
        
        if 'error' not in summary:
            print(f"\n系统资源使用:")
            print(f"  CPU使用率: 当前 {summary['cpu_usage']['current']:.1f}%, 平均 {summary['cpu_usage']['avg']:.1f}%, 峰值 {summary['cpu_usage']['max']:.1f}%")
            print(f"  内存使用率: 当前 {summary['memory_usage']['current']:.1f}%, 平均 {summary['memory_usage']['avg']:.1f}%, 峰值 {summary['memory_usage']['max']:.1f}%")
            print(f"  内存使用量: {summary['memory_usage']['current_mb']:.1f} MB")
            print(f"  活跃线程数: {summary['threads']['current']} 个")
            
            print(f"\n处理性能:")
            print(f"  当前处理速度: {summary['processing_speed']['current']:.2f} 视频/分钟")
            print(f"  平均处理速度: {summary['processing_speed']['avg']:.2f} 视频/分钟")
            print(f"  峰值处理速度: {summary['processing_speed']['max']:.2f} 视频/分钟")
            
            print(f"\n监控数据:")
            print(f"  数据点数量: {summary['data_points']} 个")
            print(f"  监控时长: {summary['time_range_minutes']:.1f} 分钟")
    
    def __enter__(self):
        """上下文管理器入口"""
        self.start_monitoring()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop_monitoring()
