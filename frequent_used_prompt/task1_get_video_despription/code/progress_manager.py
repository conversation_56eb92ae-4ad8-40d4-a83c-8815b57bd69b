#!/usr/bin/env python3
"""
线程安全进度管理器
负责保存和恢复视频处理进度，支持断点续传功能和多线程安全操作
"""

import json
import time
import os
import threading
import shutil
from pathlib import Path
from typing import Dict, List, Set, Optional
from config import PROGRESS_FILE, CHECKPOINT_INTERVAL


class ThreadSafeProgressManager:
    """线程安全进度管理器类"""

    def __init__(self, output_dir: Path):
        """
        初始化线程安全进度管理器

        Args:
            output_dir (Path): 输出目录路径
        """
        self.output_dir = output_dir
        self.progress_file = output_dir / PROGRESS_FILE
        self.backup_file = output_dir / f"{PROGRESS_FILE}.backup"
        self.checkpoint_interval = CHECKPOINT_INTERVAL

        # 线程安全锁
        self.lock = threading.RLock()  # 使用可重入锁

        # 进度数据结构
        self.progress_data = {
            'session_info': {
                'start_time': None,
                'last_update': None,
                'total_videos': 0,
                'processed_count': 0,
                'failed_count': 0,
                'skipped_count': 0,
                'active_threads': 0
            },
            'processed_videos': set(),  # 已成功处理的视频
            'failed_videos': set(),     # 处理失败的视频
            'skipped_videos': set(),    # 跳过的视频
            'current_dataset': None,    # 当前处理的数据集
            'api_status': {
                'failed_keys': set(),
                'current_key_index': 0,
                'last_failure_time': {}
            },
            'thread_info': {}  # 线程处理信息
        }

        # 自动保存计数器
        self.operation_count = 0
        self.last_save_time = time.time()

        # 确保输出目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 加载现有进度
        self.load_progress()

        print(f"🔒 初始化线程安全进度管理器: {self.progress_file}")
    
    def load_progress(self) -> bool:
        """
        从文件加载进度数据（线程安全）

        Returns:
            bool: 是否成功加载进度
        """
        with self.lock:
            # 首先尝试加载主文件
            if self.progress_file.exists():
                if self._load_from_file(self.progress_file):
                    return True
                else:
                    print("⚠️ 主进度文件损坏，尝试加载备份文件")

            # 尝试加载备份文件
            if self.backup_file.exists():
                if self._load_from_file(self.backup_file):
                    print("✅ 从备份文件恢复进度")
                    # 立即保存到主文件
                    self._save_to_file(self.progress_file)
                    return True
                else:
                    print("⚠️ 备份文件也损坏")

            print("📁 未找到有效的进度文件，将创建新的处理会话")
            self._init_new_session()
            return False

    def _load_from_file(self, file_path: Path) -> bool:
        """
        从指定文件加载进度数据

        Args:
            file_path (Path): 文件路径

        Returns:
            bool: 是否成功加载
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 转换集合数据（JSON不支持set类型）
            self.progress_data['processed_videos'] = set(data.get('processed_videos', []))
            self.progress_data['failed_videos'] = set(data.get('failed_videos', []))
            self.progress_data['skipped_videos'] = set(data.get('skipped_videos', []))
            self.progress_data['api_status']['failed_keys'] = set(data.get('api_status', {}).get('failed_keys', []))

            # 复制其他数据，确保所有必需字段存在
            session_info = data.get('session_info', {})
            self.progress_data['session_info'] = {
                'start_time': session_info.get('start_time'),
                'last_update': session_info.get('last_update'),
                'total_videos': session_info.get('total_videos', 0),
                'processed_count': session_info.get('processed_count', 0),
                'failed_count': session_info.get('failed_count', 0),
                'skipped_count': session_info.get('skipped_count', 0),
                'active_threads': session_info.get('active_threads', 0)
            }
            self.progress_data['current_dataset'] = data.get('current_dataset')
            self.progress_data['api_status']['current_key_index'] = data.get('api_status', {}).get('current_key_index', 0)
            self.progress_data['api_status']['last_failure_time'] = data.get('api_status', {}).get('last_failure_time', {})
            self.progress_data['thread_info'] = data.get('thread_info', {})

            print(f"📂 成功加载进度文件: {file_path}")
            print(f"   已处理: {len(self.progress_data['processed_videos'])} 个视频")
            print(f"   失败: {len(self.progress_data['failed_videos'])} 个视频")
            print(f"   跳过: {len(self.progress_data['skipped_videos'])} 个视频")

            return True

        except Exception as e:
            print(f"❌ 加载进度文件失败 {file_path}: {e}")
            return False
    
    def save_progress(self, force: bool = False) -> bool:
        """
        保存进度到文件（线程安全，带备份）

        Args:
            force (bool): 是否强制保存（忽略检查点间隔）

        Returns:
            bool: 是否成功保存
        """
        with self.lock:
            current_time = time.time()

            # 检查是否需要保存
            if not force:
                time_since_last_save = current_time - self.last_save_time
                if time_since_last_save < 5:  # 最少间隔5秒
                    return True

            try:
                # 更新会话信息
                self.progress_data['session_info']['last_update'] = time.strftime('%Y-%m-%d %H:%M:%S')
                self.progress_data['session_info']['processed_count'] = len(self.progress_data['processed_videos'])
                self.progress_data['session_info']['failed_count'] = len(self.progress_data['failed_videos'])
                self.progress_data['session_info']['skipped_count'] = len(self.progress_data['skipped_videos'])

                # 准备保存数据（转换set为list）
                save_data = {
                    'session_info': self.progress_data['session_info'],
                    'processed_videos': list(self.progress_data['processed_videos']),
                    'failed_videos': list(self.progress_data['failed_videos']),
                    'skipped_videos': list(self.progress_data['skipped_videos']),
                    'current_dataset': self.progress_data['current_dataset'],
                    'api_status': {
                        'failed_keys': list(self.progress_data['api_status']['failed_keys']),
                        'current_key_index': self.progress_data['api_status']['current_key_index'],
                        'last_failure_time': self.progress_data['api_status']['last_failure_time']
                    },
                    'thread_info': self.progress_data['thread_info']
                }

                # 先保存到备份文件
                if self.progress_file.exists():
                    shutil.copy2(self.progress_file, self.backup_file)

                # 保存到主文件
                success = self._save_to_file(self.progress_file, save_data)

                if success:
                    self.last_save_time = current_time
                    if force:
                        print(f"💾 强制保存进度: {self.progress_file}")
                    return True
                else:
                    return False

            except Exception as e:
                print(f"❌ 保存进度失败: {e}")
                return False

    def _save_to_file(self, file_path: Path, data: Dict = None) -> bool:
        """
        保存数据到指定文件

        Args:
            file_path (Path): 文件路径
            data (Dict): 要保存的数据，如果为None则使用当前进度数据

        Returns:
            bool: 是否成功保存
        """
        try:
            if data is None:
                # 准备当前进度数据
                data = {
                    'session_info': self.progress_data['session_info'],
                    'processed_videos': list(self.progress_data['processed_videos']),
                    'failed_videos': list(self.progress_data['failed_videos']),
                    'skipped_videos': list(self.progress_data['skipped_videos']),
                    'current_dataset': self.progress_data['current_dataset'],
                    'api_status': {
                        'failed_keys': list(self.progress_data['api_status']['failed_keys']),
                        'current_key_index': self.progress_data['api_status']['current_key_index'],
                        'last_failure_time': self.progress_data['api_status']['last_failure_time']
                    },
                    'thread_info': self.progress_data['thread_info']
                }

            # 原子写入：先写入临时文件，然后重命名
            temp_file = file_path.with_suffix('.tmp')
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            # 原子重命名
            temp_file.replace(file_path)
            return True

        except Exception as e:
            print(f"❌ 保存文件失败 {file_path}: {e}")
            return False
    
    def _init_new_session(self):
        """初始化新的处理会话"""
        self.progress_data['session_info']['start_time'] = time.strftime('%Y-%m-%d %H:%M:%S')
        self.progress_data['session_info']['last_update'] = time.strftime('%Y-%m-%d %H:%M:%S')

    def is_video_processed(self, video_path: str) -> bool:
        """
        检查视频是否已经处理过（线程安全）

        Args:
            video_path (str): 视频文件路径

        Returns:
            bool: 是否已处理
        """
        with self.lock:
            return video_path in self.progress_data['processed_videos']

    def is_video_failed(self, video_path: str) -> bool:
        """
        检查视频是否处理失败过（线程安全）

        Args:
            video_path (str): 视频文件路径

        Returns:
            bool: 是否失败过
        """
        with self.lock:
            return video_path in self.progress_data['failed_videos']

    def mark_video_processed(self, video_path: str, thread_id: int = None):
        """
        标记视频为已处理（线程安全）

        Args:
            video_path (str): 视频文件路径
            thread_id (int): 处理线程ID
        """
        with self.lock:
            self.progress_data['processed_videos'].add(video_path)
            # 从失败列表中移除（如果存在）
            self.progress_data['failed_videos'].discard(video_path)

            # 记录线程信息
            if thread_id:
                if str(thread_id) not in self.progress_data['thread_info']:
                    self.progress_data['thread_info'][str(thread_id)] = {
                        'processed_count': 0,
                        'failed_count': 0,
                        'last_activity': None
                    }
                self.progress_data['thread_info'][str(thread_id)]['processed_count'] += 1
                self.progress_data['thread_info'][str(thread_id)]['last_activity'] = time.strftime('%Y-%m-%d %H:%M:%S')

            self._auto_save()

    def mark_video_failed(self, video_path: str, thread_id: int = None):
        """
        标记视频处理失败（线程安全）

        Args:
            video_path (str): 视频文件路径
            thread_id (int): 处理线程ID
        """
        with self.lock:
            self.progress_data['failed_videos'].add(video_path)

            # 记录线程信息
            if thread_id:
                if str(thread_id) not in self.progress_data['thread_info']:
                    self.progress_data['thread_info'][str(thread_id)] = {
                        'processed_count': 0,
                        'failed_count': 0,
                        'last_activity': None
                    }
                self.progress_data['thread_info'][str(thread_id)]['failed_count'] += 1
                self.progress_data['thread_info'][str(thread_id)]['last_activity'] = time.strftime('%Y-%m-%d %H:%M:%S')

            self._auto_save()

    def mark_video_skipped(self, video_path: str):
        """
        标记视频为跳过（线程安全）

        Args:
            video_path (str): 视频文件路径
        """
        with self.lock:
            self.progress_data['skipped_videos'].add(video_path)
            self._auto_save()

    def _auto_save(self):
        """自动保存检查"""
        self.operation_count += 1
        if self.operation_count % 10 == 0:  # 每10个操作自动保存一次
            self.save_progress()
    
    def set_total_videos(self, total: int):
        """
        设置总视频数量（线程安全）

        Args:
            total (int): 总视频数量
        """
        with self.lock:
            self.progress_data['session_info']['total_videos'] = total

    def set_current_dataset(self, dataset_name: str):
        """
        设置当前处理的数据集（线程安全）

        Args:
            dataset_name (str): 数据集名称
        """
        with self.lock:
            self.progress_data['current_dataset'] = dataset_name

    def get_remaining_videos(self, all_videos: List[str]) -> List[str]:
        """
        获取剩余未处理的视频列表（线程安全）

        Args:
            all_videos (List[str]): 所有视频文件路径列表

        Returns:
            List[str]: 剩余未处理的视频列表
        """
        with self.lock:
            processed_and_skipped = self.progress_data['processed_videos'] | self.progress_data['skipped_videos']
            return [video for video in all_videos if video not in processed_and_skipped]

    def get_failed_videos(self) -> Set[str]:
        """
        获取处理失败的视频列表（线程安全）

        Returns:
            Set[str]: 失败的视频集合
        """
        with self.lock:
            return self.progress_data['failed_videos'].copy()

    def set_active_threads(self, count: int):
        """
        设置活跃线程数量

        Args:
            count (int): 活跃线程数量
        """
        with self.lock:
            self.progress_data['session_info']['active_threads'] = count
    
    def get_statistics(self) -> Dict:
        """
        获取处理统计信息（线程安全）

        Returns:
            Dict: 统计信息字典
        """
        with self.lock:
            return {
                'total_videos': self.progress_data['session_info']['total_videos'],
                'processed_count': len(self.progress_data['processed_videos']),
                'failed_count': len(self.progress_data['failed_videos']),
                'skipped_count': len(self.progress_data['skipped_videos']),
                'remaining_count': self.progress_data['session_info']['total_videos'] -
                                 len(self.progress_data['processed_videos']) -
                                 len(self.progress_data['skipped_videos']),
                'start_time': self.progress_data['session_info']['start_time'],
                'last_update': self.progress_data['session_info']['last_update'],
                'active_threads': self.progress_data['session_info']['active_threads'],
                'thread_info': dict(self.progress_data['thread_info'])
            }
    
    def print_statistics(self):
        """打印处理统计信息"""
        stats = self.get_statistics()
        print(f"\n{'='*60}")
        print("处理进度统计 (多线程版):")
        print(f"{'='*60}")
        print(f"总视频数量: {stats['total_videos']}")
        print(f"已处理: {stats['processed_count']}")
        print(f"处理失败: {stats['failed_count']}")
        print(f"跳过: {stats['skipped_count']}")
        print(f"剩余: {stats['remaining_count']}")
        print(f"活跃线程: {stats['active_threads']}")

        if stats['total_videos'] > 0:
            progress_rate = (stats['processed_count'] + stats['skipped_count']) / stats['total_videos'] * 100
            success_rate = stats['processed_count'] / stats['total_videos'] * 100 if stats['total_videos'] > 0 else 0
            print(f"总进度: {progress_rate:.1f}%")
            print(f"成功率: {success_rate:.1f}%")

        print(f"开始时间: {stats['start_time']}")
        print(f"最后更新: {stats['last_update']}")

        # 显示线程统计
        if stats['thread_info']:
            print(f"\n线程处理统计:")
            for thread_id, info in stats['thread_info'].items():
                print(f"  线程 {thread_id}: 成功 {info['processed_count']}, 失败 {info['failed_count']}, 最后活动 {info['last_activity']}")

    def cleanup_progress_file(self):
        """清理进度文件和备份文件"""
        with self.lock:
            try:
                if self.progress_file.exists():
                    self.progress_file.unlink()
                    print(f"🗑️ 已清理进度文件: {self.progress_file}")

                if self.backup_file.exists():
                    self.backup_file.unlink()
                    print(f"🗑️ 已清理备份文件: {self.backup_file}")

            except Exception as e:
                print(f"❌ 清理进度文件失败: {e}")


# 为了向后兼容，创建别名
ProgressManager = ThreadSafeProgressManager
