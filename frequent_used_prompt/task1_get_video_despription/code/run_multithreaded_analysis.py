#!/usr/bin/env python3
"""
多线程视频分析主运行脚本
"""

import sys
import argparse
from pathlib import Path
from multithreaded_video_processor import MultithreadedVideoProcessor
from config import DEFAULT_INPUT_DIR, DEFAULT_OUTPUT_DIR, MAX_FILE_SIZE_MB


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='多线程视频分析处理工具')
    parser.add_argument('--input', '-i', default=DEFAULT_INPUT_DIR,
                       help='输入视频目录路径')
    parser.add_argument('--output', '-o', default=DEFAULT_OUTPUT_DIR,
                       help='输出结果目录路径')
    parser.add_argument('--max-size', '-s', type=int, default=MAX_FILE_SIZE_MB,
                       help='最大文件大小限制(MB)，超过此大小将压缩')
    parser.add_argument('--threads', '-t', type=int, default=3,
                       help='工作线程数量 (默认: 3)')
    parser.add_argument('--no-resume', action='store_true',
                       help='禁用断点续传，从头开始处理')
    parser.add_argument('--clean-progress', action='store_true',
                       help='清理进度文件，重新开始')

    args = parser.parse_args()

    # 验证输入目录
    input_path = Path(args.input)
    if not input_path.exists():
        print(f"❌ 输入目录不存在: {input_path}")
        sys.exit(1)

    # 验证线程数量
    if args.threads < 1 or args.threads > 10:
        print(f"❌ 线程数量必须在1-10之间，当前: {args.threads}")
        sys.exit(1)

    # 创建处理器
    processor = MultithreadedVideoProcessor(
        input_dir=args.input,
        output_dir=args.output,
        max_file_size_mb=args.max_size,
        resume=not args.no_resume,
        num_threads=args.threads
    )

    # 清理进度文件
    if args.clean_progress:
        processor.progress_manager.cleanup_progress_file()
        print("🗑️ 已清理进度文件")
        return

    # 处理所有视频
    print(f"🚀 开始多线程视频处理")
    print(f"   输入目录: {args.input}")
    print(f"   输出目录: {args.output}")
    print(f"   文件大小限制: {args.max_size}MB")
    print(f"   工作线程数: {args.threads}")
    print(f"   断点续传: {'禁用' if args.no_resume else '启用'}")
    print("-" * 80)

    try:
        processor.process_all_videos()
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断处理")
        print("💾 保存当前进度...")
        processor.progress_manager.save_progress(force=True)
        print("✅ 进度已保存，可以稍后继续处理")
    except Exception as e:
        print(f"\n❌ 处理过程中发生错误: {e}")
        print("💾 保存当前进度...")
        processor.progress_manager.save_progress(force=True)
        raise


if __name__ == "__main__":
    main()
